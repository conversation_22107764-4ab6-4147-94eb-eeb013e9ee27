[{"num": 1, "hier": "tb.pipeline.memory_ctrl.mmu[29].cache_unit[12]", "time_ns": 6073694.0, "check_info": "Removal time violation on signal valid", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:41", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 2, "hier": "tb.debug_unit.pcie_if.interrupt_ctrl.branch_pred[5].interrupt_ctrl", "time_ns": 8838296.0, "check_info": "Recovery time violation on signal done[4]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:42", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 3, "hier": "tb.decode_unit[8].writeback_unit.cache_unit.cache_unit[18].decode_unit.interrupt_ctrl", "time_ns": 5741857.0, "check_info": "Skew violation on signal rst_n", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:42", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 4, "hier": "tb.debug_unit.debug_unit.memory_ctrl.cpu_top[10].cache_unit[23].interrupt_ctrl", "time_ns": 893095.0, "check_info": "Recovery time violation on signal wr_en[51]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 5, "hier": "tb.interrupt_ctrl.debug_unit.decode_unit.debug_unit.pcie_if[13]", "time_ns": 7589943.0, "check_info": "Pulse width violation on signal clk", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:42", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 6, "hier": "tb.pipeline.fetch_unit.pipeline[23].cpu_top.branch_pred", "time_ns": 462150.0, "check_info": "Period violation on signal status", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 7, "hier": "tb.pipeline.cpu_top[5].memory_ctrl[26].writeback_unit.tlb", "time_ns": 8156062.0, "check_info": "Skew violation on signal error", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:42", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 8, "hier": "tb.branch_pred.mmu[24].branch_pred.execute_unit.interrupt_ctrl", "time_ns": 8160570.0, "check_info": "Setup time violation on signal data[2]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:43", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 9, "hier": "tb.fetch_unit[25].tlb[15].cache_unit[0]", "time_ns": 4434342.0, "check_info": "Hold time violation on signal cs", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 10, "hier": "tb.writeback_unit.branch_pred.decode_unit[21].pipeline", "time_ns": 9917000.0, "check_info": "Pulse width violation on signal mode", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:43", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 11, "hier": "tb.cache_unit.writeback_unit.execute_unit", "time_ns": 6402000.0, "check_info": "Recovery time violation on signal intr[51]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:43", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 12, "hier": "tb.cpu_top.tlb.tlb.mmu[26].memory_ctrl[14].branch_pred", "time_ns": 7045831.0, "check_info": "Pulse width violation on signal enable[9]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:43", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 13, "hier": "tb.pcie_if.memory_ctrl.tlb.fetch_unit.debug_unit[1]", "time_ns": 1470278.0, "check_info": "Removal time violation on signal error[60]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 14, "hier": "tb.interrupt_ctrl[18].debug_unit.interrupt_ctrl.pcie_if[9].mmu", "time_ns": 3827036.0, "check_info": "Pulse width violation on signal rd_en", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 15, "hier": "tb.interrupt_ctrl.interrupt_ctrl.debug_unit[0].pipeline", "time_ns": 5721000.0, "check_info": "Period violation on signal mode", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:43", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 16, "hier": "tb.mmu.cache_unit[16].memory_ctrl", "time_ns": 2466051.0, "check_info": "Setup time violation on signal ack", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 17, "hier": "tb.cache_unit.tlb.execute_unit[24].interrupt_ctrl.fetch_unit.decode_unit", "time_ns": 3278704.0, "check_info": "Recovery time violation on signal error", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 18, "hier": "tb.interrupt_ctrl.pipeline.execute_unit.writeback_unit.tlb.debug_unit", "time_ns": 4136000.0, "check_info": "Hold time violation on signal valid", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 19, "hier": "tb.pipeline.debug_unit[15].decode_unit", "time_ns": 5379671.0, "check_info": "Recovery time violation on signal busy[11]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:44", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 20, "hier": "tb.cpu_top[2].execute_unit.interrupt_ctrl.fetch_unit[8].memory_ctrl.mmu", "time_ns": 3265057.0, "check_info": "Setup time violation on signal idle", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 21, "hier": "tb.mmu.ddr_ctrl[20].mmu.ddr_ctrl.branch_pred.writeback_unit", "time_ns": 8361996.0, "check_info": "Recovery time violation on signal clk", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:44", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 22, "hier": "tb.debug_unit.decode_unit.cache_unit[3].execute_unit[26]", "time_ns": 8493000.0, "check_info": "Period violation on signal grant", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:44", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 23, "hier": "tb.cpu_top.mmu.pcie_if[9].pcie_if.cpu_top.interrupt_ctrl", "time_ns": 1037695.0, "check_info": "Removal time violation on signal ack", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 24, "hier": "tb.pcie_if.branch_pred.decode_unit.pipeline.interrupt_ctrl.branch_pred", "time_ns": 2002711.0, "check_info": "Period violation on signal grant", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 25, "hier": "tb.execute_unit.cache_unit.fetch_unit.writeback_unit.memory_ctrl[2]", "time_ns": 2046921.0, "check_info": "Period violation on signal ack", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 26, "hier": "tb.interrupt_ctrl[5].decode_unit.memory_ctrl.cpu_top[23].writeback_unit.decode_unit", "time_ns": 6541000.0, "check_info": "Hold time violation on signal valid[58]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:44", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 27, "hier": "tb.branch_pred.writeback_unit.pcie_if.decode_unit[7]", "time_ns": 9694065.0, "check_info": "Hold time violation on signal valid", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:45", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 28, "hier": "tb.debug_unit[5].debug_unit[7].ddr_ctrl", "time_ns": 2214000.0, "check_info": "Skew violation on signal enable", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 29, "hier": "tb.tlb.cache_unit.debug_unit.memory_ctrl", "time_ns": 7341278.0, "check_info": "Removal time violation on signal error[45]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:45", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 30, "hier": "tb.pipeline.decode_unit.fetch_unit.mmu[24].execute_unit.branch_pred[25]", "time_ns": 9327144.0, "check_info": "Recovery time violation on signal grant[32]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:45", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 31, "hier": "tb.cpu_top[4].execute_unit[2].mmu.writeback_unit.debug_unit", "time_ns": 8645230.0, "check_info": "Pulse width violation on signal data[19]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:45", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 32, "hier": "tb.tlb.ddr_ctrl.cache_unit", "time_ns": 6016467.0, "check_info": "Setup time violation on signal rst_n[11]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:45", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 33, "hier": "tb.interrupt_ctrl.tlb[5].tlb[13].fetch_unit[12].pipeline[12]", "time_ns": 8285000.0, "check_info": "Period violation on signal data", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:46", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 34, "hier": "tb.fetch_unit.writeback_unit.pipeline.cpu_top[1].mmu[24].pipeline", "time_ns": 5608969.0, "check_info": "Hold time violation on signal mode", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:46", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 35, "hier": "tb.memory_ctrl.cache_unit.pipeline", "time_ns": 8882245.0, "check_info": "Removal time violation on signal valid", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:46", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 36, "hier": "tb.fetch_unit.memory_ctrl.fetch_unit.interrupt_ctrl", "time_ns": 1261000.0, "check_info": "Pulse width violation on signal enable", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 37, "hier": "tb.fetch_unit[28].mmu.execute_unit[15].writeback_unit.debug_unit.interrupt_ctrl", "time_ns": 9454000.0, "check_info": "Period violation on signal clk[50]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:46", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 38, "hier": "tb.fetch_unit.cache_unit[13].mmu.pipeline[4]", "time_ns": 9652000.0, "check_info": "Period violation on signal busy", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:46", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 39, "hier": "tb.mmu.branch_pred[7].cpu_top[0].branch_pred[20].cache_unit", "time_ns": 3693453.0, "check_info": "Skew violation on signal mode[31]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 40, "hier": "tb.debug_unit[31].cache_unit.memory_ctrl.tlb", "time_ns": 9463669.0, "check_info": "Setup time violation on signal rst_n[6]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:47", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 41, "hier": "tb.fetch_unit[21].mmu.branch_pred.interrupt_ctrl.debug_unit", "time_ns": 3813917.0, "check_info": "Removal time violation on signal valid[19]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 42, "hier": "tb.fetch_unit.writeback_unit[19].pipeline[6].cpu_top[10].decode_unit.ddr_ctrl", "time_ns": 2978951.0, "check_info": "Period violation on signal addr", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 43, "hier": "tb.cache_unit.pcie_if.fetch_unit.execute_unit.ddr_ctrl", "time_ns": 6754428.0, "check_info": "Pulse width violation on signal intr", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:47", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 44, "hier": "tb.decode_unit.cache_unit.cache_unit.pcie_if.cpu_top[18]", "time_ns": 2993739.0, "check_info": "Removal time violation on signal intr[36]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 45, "hier": "tb.branch_pred.pcie_if.mmu[22].debug_unit", "time_ns": 6441000.0, "check_info": "Recovery time violation on signal mode[22]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:47", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 46, "hier": "tb.pipeline[29].branch_pred.memory_ctrl[13]", "time_ns": 2996531.0, "check_info": "Period violation on signal mode", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 47, "hier": "tb.ddr_ctrl.pcie_if[22].pipeline[18]", "time_ns": 5938838.0, "check_info": "Setup time violation on signal addr", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:47", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 48, "hier": "tb.branch_pred.mmu.interrupt_ctrl.memory_ctrl[29]", "time_ns": 5389000.0, "check_info": "Removal time violation on signal ready", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:48", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 49, "hier": "tb.fetch_unit.branch_pred[11].execute_unit.interrupt_ctrl.pcie_if.pipeline[19]", "time_ns": 729587.0, "check_info": "Hold time violation on signal error[17]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 50, "hier": "tb.tlb[13].memory_ctrl.pipeline.mmu.decode_unit.memory_ctrl", "time_ns": 4348884.0, "check_info": "Period violation on signal intr[13]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 51, "hier": "tb.writeback_unit.cpu_top.ddr_ctrl", "time_ns": 963000.0, "check_info": "Period violation on signal rd_en[20]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 52, "hier": "tb.memory_ctrl[14].writeback_unit[1].memory_ctrl[20]", "time_ns": 2703663.0, "check_info": "Skew violation on signal ready[2]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 53, "hier": "tb.tlb.interrupt_ctrl[6].memory_ctrl[1].memory_ctrl[5]", "time_ns": 4739000.0, "check_info": "Hold time violation on signal clk[50]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 54, "hier": "tb.decode_unit.fetch_unit.pipeline.tlb[16].decode_unit", "time_ns": 9397971.0, "check_info": "Period violation on signal mode[33]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:48", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 55, "hier": "tb.debug_unit.pcie_if.memory_ctrl.cache_unit.execute_unit.pcie_if", "time_ns": 3245386.0, "check_info": "Hold time violation on signal valid", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 56, "hier": "tb.cpu_top.tlb[28].fetch_unit[17]", "time_ns": 2231000.0, "check_info": "Period violation on signal clk[3]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 57, "hier": "tb.tlb[17].ddr_ctrl[2].debug_unit.ddr_ctrl.ddr_ctrl", "time_ns": 6875765.0, "check_info": "Removal time violation on signal rd_en[61]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:48", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 58, "hier": "tb.mmu.ddr_ctrl.ddr_ctrl", "time_ns": 7256000.0, "check_info": "Setup time violation on signal rst_n", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:48", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 59, "hier": "tb.tlb[20].interrupt_ctrl[15].pcie_if.cache_unit.decode_unit.interrupt_ctrl", "time_ns": 5785000.0, "check_info": "Setup time violation on signal intr", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:48", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 60, "hier": "tb.branch_pred[3].memory_ctrl.decode_unit[7].mmu", "time_ns": 7267576.0, "check_info": "Hold time violation on signal error", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:49", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 61, "hier": "tb.tlb[12].debug_unit[1].interrupt_ctrl[12].ddr_ctrl", "time_ns": 6544000.0, "check_info": "Setup time violation on signal error", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:49", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 62, "hier": "tb.writeback_unit.ddr_ctrl.decode_unit[17].memory_ctrl", "time_ns": 5445209.0, "check_info": "Recovery time violation on signal data", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:49", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 63, "hier": "tb.memory_ctrl.execute_unit[19].debug_unit", "time_ns": 1272000.0, "check_info": "Hold time violation on signal req", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 64, "hier": "tb.ddr_ctrl.interrupt_ctrl.fetch_unit.mmu[3]", "time_ns": 5883000.0, "check_info": "Removal time violation on signal idle", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:49", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 65, "hier": "tb.fetch_unit[6].pcie_if.execute_unit[9]", "time_ns": 9753164.0, "check_info": "Period violation on signal ready", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:50", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 66, "hier": "tb.cpu_top.tlb.interrupt_ctrl.execute_unit", "time_ns": 8518000.0, "check_info": "Recovery time violation on signal idle[17]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:50", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 67, "hier": "tb.tlb.writeback_unit.debug_unit.ddr_ctrl.fetch_unit", "time_ns": 4184974.0, "check_info": "Hold time violation on signal grant", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 68, "hier": "tb.interrupt_ctrl[29].pcie_if.execute_unit.memory_ctrl.cache_unit", "time_ns": 7713454.0, "check_info": "Hold time violation on signal mode", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:50", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 69, "hier": "tb.fetch_unit.writeback_unit[26].memory_ctrl.mmu", "time_ns": 2808390.0, "check_info": "Skew violation on signal addr[38]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 70, "hier": "tb.branch_pred.memory_ctrl.interrupt_ctrl", "time_ns": 2446951.0, "check_info": "Removal time violation on signal grant[43]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 71, "hier": "tb.debug_unit.execute_unit.branch_pred.branch_pred.pipeline.decode_unit", "time_ns": 1094000.0, "check_info": "Period violation on signal busy", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 72, "hier": "tb.interrupt_ctrl[15].pipeline.decode_unit.tlb.mmu[15]", "time_ns": 4848000.0, "check_info": "Period violation on signal error[22]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 73, "hier": "tb.memory_ctrl[6].mmu[30].mmu.debug_unit[9].interrupt_ctrl", "time_ns": 5799539.0, "check_info": "Pulse width violation on signal ack[23]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:50", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 74, "hier": "tb.cache_unit.decode_unit.ddr_ctrl.interrupt_ctrl.tlb", "time_ns": 4469000.0, "check_info": "Period violation on signal intr", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 75, "hier": "tb.cpu_top.fetch_unit[29].fetch_unit.decode_unit[1].decode_unit", "time_ns": 5847539.0, "check_info": "Skew violation on signal grant", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:51", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 76, "hier": "tb.debug_unit.writeback_unit.tlb.mmu[17]", "time_ns": 6525000.0, "check_info": "Period violation on signal rst_n", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:51", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 77, "hier": "tb.pcie_if.debug_unit[27].execute_unit.branch_pred[15].execute_unit.cache_unit", "time_ns": 6201772.0, "check_info": "Pulse width violation on signal error", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:51", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 78, "hier": "tb.cpu_top.mmu[8].memory_ctrl.branch_pred", "time_ns": 7414000.0, "check_info": "Recovery time violation on signal data[46]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:51", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 79, "hier": "tb.tlb[30].debug_unit.cpu_top[7].fetch_unit.cpu_top[20].tlb[14]", "time_ns": 4748222.0, "check_info": "Recovery time violation on signal mode", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 80, "hier": "tb.mmu.cache_unit.fetch_unit.ddr_ctrl[3].ddr_ctrl[9]", "time_ns": 3823482.0, "check_info": "Skew violation on signal error[34]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 81, "hier": "tb.cpu_top.interrupt_ctrl.writeback_unit.pipeline[6]", "time_ns": 5912878.0, "check_info": "Setup time violation on signal clk", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:51", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 82, "hier": "tb.cpu_top.tlb[20].execute_unit", "time_ns": 7405000.0, "check_info": "Pulse width violation on signal ready", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:52", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 83, "hier": "tb.cache_unit.execute_unit.pipeline.cpu_top.cache_unit", "time_ns": 9559931.0, "check_info": "Period violation on signal mode", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:52", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 84, "hier": "tb.ddr_ctrl.pipeline.fetch_unit.mmu.memory_ctrl[25].cache_unit", "time_ns": 4741110.0, "check_info": "Recovery time violation on signal done[52]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 85, "hier": "tb.writeback_unit.interrupt_ctrl[19].ddr_ctrl", "time_ns": 431411.0, "check_info": "Hold time violation on signal error[2]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 86, "hier": "tb.pcie_if[24].mmu.debug_unit", "time_ns": 3951040.0, "check_info": "Skew violation on signal mode", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 87, "hier": "tb.mmu.mmu.tlb", "time_ns": 189758.0, "check_info": "Removal time violation on signal addr[63]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 88, "hier": "tb.writeback_unit.cpu_top.execute_unit.decode_unit.decode_unit.branch_pred", "time_ns": 5220345.0, "check_info": "Recovery time violation on signal ack[45]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:52", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 89, "hier": "tb.mmu.interrupt_ctrl.fetch_unit.pipeline[18].pcie_if", "time_ns": 3443000.0, "check_info": "Removal time violation on signal addr[13]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 90, "hier": "tb.decode_unit.cpu_top.interrupt_ctrl.pcie_if.mmu", "time_ns": 7591000.0, "check_info": "Recovery time violation on signal rd_en", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:52", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 91, "hier": "tb.ddr_ctrl[14].debug_unit[29].debug_unit.fetch_unit.decode_unit.memory_ctrl[25]", "time_ns": 1761414.0, "check_info": "Hold time violation on signal rst_n[20]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 92, "hier": "tb.debug_unit[30].pcie_if.execute_unit", "time_ns": 4975616.0, "check_info": "Skew violation on signal rst_n[24]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 93, "hier": "tb.tlb[26].cpu_top.branch_pred[26]", "time_ns": 7617000.0, "check_info": "Removal time violation on signal enable[60]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:53", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 94, "hier": "tb.branch_pred[20].cache_unit.cache_unit", "time_ns": 666000.0, "check_info": "Pulse width violation on signal addr", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 95, "hier": "tb.decode_unit.ddr_ctrl[0].tlb.interrupt_ctrl.writeback_unit", "time_ns": 323000.0, "check_info": "Setup time violation on signal ready", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 96, "hier": "tb.tlb[1].pipeline[16].pcie_if.decode_unit[24]", "time_ns": 9189267.0, "check_info": "Period violation on signal valid[57]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:53", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 97, "hier": "tb.pipeline.pipeline.interrupt_ctrl.writeback_unit[25]", "time_ns": 4542541.0, "check_info": "Setup time violation on signal rd_en[6]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 98, "hier": "tb.cpu_top.execute_unit.memory_ctrl.writeback_unit", "time_ns": 8360855.0, "check_info": "Period violation on signal busy", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:53", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 99, "hier": "tb.writeback_unit.cache_unit.fetch_unit.tlb.debug_unit[4]", "time_ns": 9820218.0, "check_info": "Period violation on signal ready[62]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:53", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 100, "hier": "tb.cpu_top.fetch_unit[13].interrupt_ctrl.pcie_if[25]", "time_ns": 8660022.0, "check_info": "Period violation on signal clk", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:53", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 101, "hier": "tb.cpu_top.fetch_unit[13].interrupt_ctrl.pcie_if[26]", "time_ns": 866002233.0, "check_info": "setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:54", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 102, "hier": "tb.cpu_top.fetch_unit[13].interrupt_ctrl.pcie_if[27]", "time_ns": 866002234.0, "check_info": "setuphold<hold>( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:54", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 103, "hier": "tb.cpu_top.fetch_unit[13].interrupt_ctrl.pcie_if[28]", "time_ns": 866002234.0, "check_info": "hold( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:54", "corner": "npg_f1_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 1, "hier": "tb.pipeline.memory_ctrl.mmu[29].cache_unit[12]", "time_ns": 6073694.0, "check_info": "Removal time violation on signal valid", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 2, "hier": "tb.debug_unit.pcie_if.interrupt_ctrl.branch_pred[5].interrupt_ctrl", "time_ns": 8838296.0, "check_info": "Recovery time violation on signal done[4]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 3, "hier": "tb.decode_unit[8].writeback_unit.cache_unit.cache_unit[18].decode_unit.interrupt_ctrl", "time_ns": 5741857.0, "check_info": "Skew violation on signal rst_n", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 4, "hier": "tb.debug_unit.debug_unit.memory_ctrl.cpu_top[10].cache_unit[23].interrupt_ctrl", "time_ns": 893095.0, "check_info": "Recovery time violation on signal wr_en[51]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:22", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 5, "hier": "tb.interrupt_ctrl.debug_unit.decode_unit.debug_unit.pcie_if[13]", "time_ns": 7589943.0, "check_info": "Pulse width violation on signal clk", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 6, "hier": "tb.pipeline.fetch_unit.pipeline[23].cpu_top.branch_pred", "time_ns": 462150.0, "check_info": "Period violation on signal status", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:22", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 7, "hier": "tb.pipeline.cpu_top[5].memory_ctrl[26].writeback_unit.tlb", "time_ns": 8156062.0, "check_info": "Skew violation on signal error", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 8, "hier": "tb.branch_pred.mmu[24].branch_pred.execute_unit.interrupt_ctrl", "time_ns": 8160570.0, "check_info": "Setup time violation on signal data[2]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 9, "hier": "tb.fetch_unit[25].tlb[15].cache_unit[0]", "time_ns": 4434342.0, "check_info": "Hold time violation on signal cs", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:22", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 10, "hier": "tb.writeback_unit.branch_pred.decode_unit[21].pipeline", "time_ns": 9917000.0, "check_info": "Pulse width violation on signal mode", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 11, "hier": "tb.cache_unit.writeback_unit.execute_unit", "time_ns": 6402000.0, "check_info": "Recovery time violation on signal intr[51]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 12, "hier": "tb.cpu_top.tlb.tlb.mmu[26].memory_ctrl[14].branch_pred", "time_ns": 7045831.0, "check_info": "Pulse width violation on signal enable[9]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 13, "hier": "tb.pcie_if.memory_ctrl.tlb.fetch_unit.debug_unit[1]", "time_ns": 1470278.0, "check_info": "Removal time violation on signal error[60]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:23", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 14, "hier": "tb.interrupt_ctrl[18].debug_unit.interrupt_ctrl.pcie_if[9].mmu", "time_ns": 3827036.0, "check_info": "Pulse width violation on signal rd_en", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:23", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 15, "hier": "tb.interrupt_ctrl.interrupt_ctrl.debug_unit[0].pipeline", "time_ns": 5721000.0, "check_info": "Period violation on signal mode", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 16, "hier": "tb.mmu.cache_unit[16].memory_ctrl", "time_ns": 2466051.0, "check_info": "Setup time violation on signal ack", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:23", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 17, "hier": "tb.cache_unit.tlb.execute_unit[24].interrupt_ctrl.fetch_unit.decode_unit", "time_ns": 3278704.0, "check_info": "Recovery time violation on signal error", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:23", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 18, "hier": "tb.interrupt_ctrl.pipeline.execute_unit.writeback_unit.tlb.debug_unit", "time_ns": 4136000.0, "check_info": "Hold time violation on signal valid", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:24", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 19, "hier": "tb.pipeline.debug_unit[15].decode_unit", "time_ns": 5379671.0, "check_info": "Recovery time violation on signal busy[11]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 20, "hier": "tb.cpu_top[2].execute_unit.interrupt_ctrl.fetch_unit[8].memory_ctrl.mmu", "time_ns": 3265057.0, "check_info": "Setup time violation on signal idle", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:24", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 21, "hier": "tb.mmu.ddr_ctrl[20].mmu.ddr_ctrl.branch_pred.writeback_unit", "time_ns": 8361996.0, "check_info": "Recovery time violation on signal clk", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 22, "hier": "tb.debug_unit.decode_unit.cache_unit[3].execute_unit[26]", "time_ns": 8493000.0, "check_info": "Period violation on signal grant", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 23, "hier": "tb.cpu_top.mmu.pcie_if[9].pcie_if.cpu_top.interrupt_ctrl", "time_ns": 1037695.0, "check_info": "Removal time violation on signal ack", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:24", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 24, "hier": "tb.pcie_if.branch_pred.decode_unit.pipeline.interrupt_ctrl.branch_pred", "time_ns": 2002711.0, "check_info": "Period violation on signal grant", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:24", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 25, "hier": "tb.execute_unit.cache_unit.fetch_unit.writeback_unit.memory_ctrl[2]", "time_ns": 2046921.0, "check_info": "Period violation on signal ack", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:25", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 26, "hier": "tb.interrupt_ctrl[5].decode_unit.memory_ctrl.cpu_top[23].writeback_unit.decode_unit", "time_ns": 6541000.0, "check_info": "Hold time violation on signal valid[58]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 27, "hier": "tb.branch_pred.writeback_unit.pcie_if.decode_unit[7]", "time_ns": 9694065.0, "check_info": "Hold time violation on signal valid", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 28, "hier": "tb.debug_unit[5].debug_unit[7].ddr_ctrl", "time_ns": 2214000.0, "check_info": "Skew violation on signal enable", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:25", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 29, "hier": "tb.tlb.cache_unit.debug_unit.memory_ctrl", "time_ns": 7341278.0, "check_info": "Removal time violation on signal error[45]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 30, "hier": "tb.pipeline.decode_unit.fetch_unit.mmu[24].execute_unit.branch_pred[25]", "time_ns": 9327144.0, "check_info": "Recovery time violation on signal grant[32]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 31, "hier": "tb.cpu_top[4].execute_unit[2].mmu.writeback_unit.debug_unit", "time_ns": 8645230.0, "check_info": "Pulse width violation on signal data[19]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 32, "hier": "tb.tlb.ddr_ctrl.cache_unit", "time_ns": 6016467.0, "check_info": "Setup time violation on signal rst_n[11]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 33, "hier": "tb.interrupt_ctrl.tlb[5].tlb[13].fetch_unit[12].pipeline[12]", "time_ns": 8285000.0, "check_info": "Period violation on signal data", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 34, "hier": "tb.fetch_unit.writeback_unit.pipeline.cpu_top[1].mmu[24].pipeline", "time_ns": 5608969.0, "check_info": "Hold time violation on signal mode", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 35, "hier": "tb.memory_ctrl.cache_unit.pipeline", "time_ns": 8882245.0, "check_info": "Removal time violation on signal valid", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 36, "hier": "tb.fetch_unit.memory_ctrl.fetch_unit.interrupt_ctrl", "time_ns": 1261000.0, "check_info": "Pulse width violation on signal enable", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:25", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 37, "hier": "tb.fetch_unit[28].mmu.execute_unit[15].writeback_unit.debug_unit.interrupt_ctrl", "time_ns": 9454000.0, "check_info": "Period violation on signal clk[50]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 38, "hier": "tb.fetch_unit.cache_unit[13].mmu.pipeline[4]", "time_ns": 9652000.0, "check_info": "Period violation on signal busy", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 39, "hier": "tb.mmu.branch_pred[7].cpu_top[0].branch_pred[20].cache_unit", "time_ns": 3693453.0, "check_info": "Skew violation on signal mode[31]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:25", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 40, "hier": "tb.debug_unit[31].cache_unit.memory_ctrl.tlb", "time_ns": 9463669.0, "check_info": "Setup time violation on signal rst_n[6]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 41, "hier": "tb.fetch_unit[21].mmu.branch_pred.interrupt_ctrl.debug_unit", "time_ns": 3813917.0, "check_info": "Removal time violation on signal valid[19]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:26", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 42, "hier": "tb.fetch_unit.writeback_unit[19].pipeline[6].cpu_top[10].decode_unit.ddr_ctrl", "time_ns": 2978951.0, "check_info": "Period violation on signal addr", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:26", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 43, "hier": "tb.cache_unit.pcie_if.fetch_unit.execute_unit.ddr_ctrl", "time_ns": 6754428.0, "check_info": "Pulse width violation on signal intr", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 44, "hier": "tb.decode_unit.cache_unit.cache_unit.pcie_if.cpu_top[18]", "time_ns": 2993739.0, "check_info": "Removal time violation on signal intr[36]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:26", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 45, "hier": "tb.branch_pred.pcie_if.mmu[22].debug_unit", "time_ns": 6441000.0, "check_info": "Recovery time violation on signal mode[22]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 46, "hier": "tb.pipeline[29].branch_pred.memory_ctrl[13]", "time_ns": 2996531.0, "check_info": "Period violation on signal mode", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:26", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 47, "hier": "tb.ddr_ctrl.pcie_if[22].pipeline[18]", "time_ns": 5938838.0, "check_info": "Setup time violation on signal addr", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 48, "hier": "tb.branch_pred.mmu.interrupt_ctrl.memory_ctrl[29]", "time_ns": 5389000.0, "check_info": "Removal time violation on signal ready", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 49, "hier": "tb.fetch_unit.branch_pred[11].execute_unit.interrupt_ctrl.pcie_if.pipeline[19]", "time_ns": 729587.0, "check_info": "Hold time violation on signal error[17]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:26", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 50, "hier": "tb.tlb[13].memory_ctrl.pipeline.mmu.decode_unit.memory_ctrl", "time_ns": 4348884.0, "check_info": "Period violation on signal intr[13]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:27", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 51, "hier": "tb.writeback_unit.cpu_top.ddr_ctrl", "time_ns": 963000.0, "check_info": "Period violation on signal rd_en[20]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:27", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 52, "hier": "tb.memory_ctrl[14].writeback_unit[1].memory_ctrl[20]", "time_ns": 2703663.0, "check_info": "Skew violation on signal ready[2]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:27", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 53, "hier": "tb.tlb.interrupt_ctrl[6].memory_ctrl[1].memory_ctrl[5]", "time_ns": 4739000.0, "check_info": "Hold time violation on signal clk[50]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:27", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 54, "hier": "tb.decode_unit.fetch_unit.pipeline.tlb[16].decode_unit", "time_ns": 9397971.0, "check_info": "Period violation on signal mode[33]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 55, "hier": "tb.debug_unit.pcie_if.memory_ctrl.cache_unit.execute_unit.pcie_if", "time_ns": 3245386.0, "check_info": "Hold time violation on signal valid", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:27", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 56, "hier": "tb.cpu_top.tlb[28].fetch_unit[17]", "time_ns": 2231000.0, "check_info": "Period violation on signal clk[3]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:28", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 57, "hier": "tb.tlb[17].ddr_ctrl[2].debug_unit.ddr_ctrl.ddr_ctrl", "time_ns": 6875765.0, "check_info": "Removal time violation on signal rd_en[61]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 58, "hier": "tb.mmu.ddr_ctrl.ddr_ctrl", "time_ns": 7256000.0, "check_info": "Setup time violation on signal rst_n", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 59, "hier": "tb.tlb[20].interrupt_ctrl[15].pcie_if.cache_unit.decode_unit.interrupt_ctrl", "time_ns": 5785000.0, "check_info": "Setup time violation on signal intr", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 60, "hier": "tb.branch_pred[3].memory_ctrl.decode_unit[7].mmu", "time_ns": 7267576.0, "check_info": "Hold time violation on signal error", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 61, "hier": "tb.tlb[12].debug_unit[1].interrupt_ctrl[12].ddr_ctrl", "time_ns": 6544000.0, "check_info": "Setup time violation on signal error", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 62, "hier": "tb.interrupt_ctrl.branch_pred.cpu_top.cache_unit.tlb.writeback_unit", "time_ns": 8155818.0, "check_info": "Skew violation on signal idle[19]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:28", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 63, "hier": "tb.branch_pred[7].interrupt_ctrl.cpu_top.tlb.writeback_unit.tlb", "time_ns": 1678165.0, "check_info": "Period violation on signal grant[43]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:28", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 64, "hier": "tb.tlb[16].mmu.tlb.mmu[12]", "time_ns": 376220.0, "check_info": "Period violation on signal req", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:28", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 65, "hier": "tb.writeback_unit[27].pcie_if.debug_unit.ddr_ctrl", "time_ns": 4864152.0, "check_info": "Hold time violation on signal error[1]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:29", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 66, "hier": "tb.fetch_unit.pcie_if.memory_ctrl.writeback_unit", "time_ns": 1162898.0, "check_info": "Setup time violation on signal data", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:29", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 67, "hier": "tb.pipeline[9].branch_pred.tlb.pcie_if.execute_unit", "time_ns": 6777464.0, "check_info": "Recovery time violation on signal ack", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:29", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 68, "hier": "tb.ddr_ctrl.cache_unit.cache_unit", "time_ns": 9921402.0, "check_info": "Skew violation on signal enable", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:29", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 69, "hier": "tb.fetch_unit.cache_unit.cache_unit.tlb.pipeline.execute_unit", "time_ns": 9795209.0, "check_info": "Hold time violation on signal clk", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:29", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 70, "hier": "tb.pipeline.fetch_unit.cpu_top[7].branch_pred.fetch_unit[25]", "time_ns": 9242802.0, "check_info": "Period violation on signal addr[58]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:30", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 71, "hier": "tb.mmu.execute_unit.debug_unit", "time_ns": 4371015.0, "check_info": "Setup time violation on signal clk[0]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:30", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 72, "hier": "tb.execute_unit.ddr_ctrl.tlb.mmu.cache_unit.branch_pred[17]", "time_ns": 4761932.0, "check_info": "Hold time violation on signal addr[23]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:30", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 73, "hier": "tb.branch_pred.pcie_if[22].tlb.cache_unit.tlb[19].execute_unit", "time_ns": 4709000.0, "check_info": "Pulse width violation on signal data", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:30", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 74, "hier": "tb.pipeline.debug_unit[6].cache_unit.cache_unit.branch_pred[10]", "time_ns": 3125558.0, "check_info": "Removal time violation on signal done[52]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:30", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 75, "hier": "tb.branch_pred[24].pipeline.tlb.ddr_ctrl[2].branch_pred", "time_ns": 5228133.0, "check_info": "Pulse width violation on signal grant[19]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:31", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 76, "hier": "tb.pipeline[19].pcie_if[4].cache_unit.decode_unit[25].debug_unit[6]", "time_ns": 2730000.0, "check_info": "Pulse width violation on signal wr_en", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:31", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 77, "hier": "tb.debug_unit.fetch_unit.pipeline[21]", "time_ns": 6835257.0, "check_info": "Setup time violation on signal busy[33]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:31", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 78, "hier": "tb.ddr_ctrl.writeback_unit[6].fetch_unit[15].writeback_unit[17]", "time_ns": 9485334.0, "check_info": "Pulse width violation on signal wr_en[55]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:31", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 79, "hier": "tb.pipeline.branch_pred.tlb.memory_ctrl", "time_ns": 2135298.0, "check_info": "Recovery time violation on signal valid[21]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:31", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 80, "hier": "tb.execute_unit.memory_ctrl[15].debug_unit.pipeline.mmu[13]", "time_ns": 4531255.0, "check_info": "Skew violation on signal rst_n[24]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:32", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 81, "hier": "tb.decode_unit[18].pipeline.cache_unit[15]", "time_ns": 7567369.0, "check_info": "Period violation on signal busy[47]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:32", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 82, "hier": "tb.interrupt_ctrl.execute_unit[20].ddr_ctrl[27].memory_ctrl.tlb", "time_ns": 5923723.0, "check_info": "Period violation on signal error[24]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:32", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 83, "hier": "tb.cpu_top[30].pipeline.pcie_if.mmu.cache_unit[21].pipeline", "time_ns": 2121000.0, "check_info": "Period violation on signal wr_en[55]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:32", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 84, "hier": "tb.fetch_unit.writeback_unit.execute_unit.ddr_ctrl.debug_unit[13].cache_unit", "time_ns": 6752000.0, "check_info": "Pulse width violation on signal rst_n", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:32", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 85, "hier": "tb.interrupt_ctrl.tlb.debug_unit.pipeline.writeback_unit", "time_ns": 493199.0, "check_info": "Setup time violation on signal ready[11]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:33", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 86, "hier": "tb.interrupt_ctrl[17].mmu.cpu_top[24].tlb.memory_ctrl", "time_ns": 973166.0, "check_info": "Recovery time violation on signal req", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:33", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 87, "hier": "tb.writeback_unit.cache_unit.branch_pred.ddr_ctrl.branch_pred[5].decode_unit[6]", "time_ns": 5328000.0, "check_info": "Recovery time violation on signal mode[26]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:33", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 88, "hier": "tb.execute_unit[14].ddr_ctrl.cache_unit.branch_pred.cache_unit.debug_unit", "time_ns": 3889423.0, "check_info": "Hold time violation on signal ack", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:33", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 89, "hier": "tb.debug_unit[13].ddr_ctrl.mmu", "time_ns": 1874000.0, "check_info": "Removal time violation on signal idle[27]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:34", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 90, "hier": "tb.memory_ctrl.debug_unit.interrupt_ctrl", "time_ns": 7527944.0, "check_info": "Period violation on signal data[53]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:34", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 91, "hier": "tb.interrupt_ctrl.mmu.branch_pred[15].cache_unit[16].mmu", "time_ns": 6159095.0, "check_info": "Removal time violation on signal wr_en", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:34", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 92, "hier": "tb.ddr_ctrl.cpu_top.fetch_unit.writeback_unit", "time_ns": 7547946.0, "check_info": "Skew violation on signal valid[20]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:34", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 93, "hier": "tb.memory_ctrl.fetch_unit.writeback_unit.cache_unit.tlb.ddr_ctrl", "time_ns": 2095826.0, "check_info": "Hold time violation on signal idle", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:34", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 94, "hier": "tb.pipeline[22].fetch_unit.decode_unit.execute_unit", "time_ns": 5797086.0, "check_info": "Setup time violation on signal error", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:35", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 95, "hier": "tb.debug_unit.writeback_unit.pipeline", "time_ns": 6902551.0, "check_info": "Setup time violation on signal grant[2]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:35", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 96, "hier": "tb.decode_unit[20].cpu_top.mmu.debug_unit", "time_ns": 5940000.0, "check_info": "Pulse width violation on signal intr[24]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:35", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 97, "hier": "tb.tlb.branch_pred.mmu[0].memory_ctrl", "time_ns": 3282533.0, "check_info": "Skew violation on signal valid", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:36", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 98, "hier": "tb.tlb[31].decode_unit.cpu_top.cache_unit.debug_unit.writeback_unit", "time_ns": 7714725.0, "check_info": "Pulse width violation on signal intr[56]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:36", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 99, "hier": "tb.decode_unit.fetch_unit[13].cache_unit.pipeline.debug_unit", "time_ns": 2945000.0, "check_info": "Skew violation on signal idle[52]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:36", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 100, "hier": "tb.decode_unit.tlb.debug_unit.branch_pred", "time_ns": 1634876.0, "check_info": "Period violation on signal data[30]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:36", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 101, "hier": "tb.cpu_top.fetch_unit[13].interrupt_ctrl.pcie_if[26]", "time_ns": 866002233.0, "check_info": "setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:7869100 FS, negedge SE:7735500 FS,0.1621 : 262100FS, -0.0241 : -24100 FS )", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 102, "hier": "tb.cpu_top.fetch_unit[13].interrupt_ctrl.pcie_if[27]", "time_ns": 866002234.0, "check_info": "setuphold<hold>( negedge cpN && nCD nD SI SDFCHK:7869100 FS, negedge SE:7735500 FS,0.1621 : 262100FS, -0.0241 : -24100 FS )", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"num": 103, "hier": "tb.cpu_top.fetch_unit[13].interrupt_ctrl.pcie_if[28]", "time_ns": 866002234.0, "check_info": "setup( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:37", "corner": "npg_f2_ffg", "case": "page_test_027_test", "source": "database"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 1, "hier": "tb.pipeline.memory_ctrl.mmu[29].cache_unit[12]", "time_ns": 6.073694, "check_info": "Removal time violation on signal valid", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 2, "hier": "tb.debug_unit.pcie_if.interrupt_ctrl.branch_pred[5].interrupt_ctrl", "time_ns": 8.838296, "check_info": "Recovery time violation on signal done[4]", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 3, "hier": "tb.decode_unit[8].writeback_unit.cache_unit.cache_unit[18].decode_unit.interrupt_ctrl", "time_ns": 5.741857, "check_info": "Skew violation on signal rst_n", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 4, "hier": "tb.debug_unit.debug_unit.memory_ctrl.cpu_top[10].cache_unit[23].interrupt_ctrl", "time_ns": 0.893095, "check_info": "Recovery time violation on signal wr_en[51]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:22"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 5, "hier": "tb.interrupt_ctrl.debug_unit.decode_unit.debug_unit.pcie_if[13]", "time_ns": 7.589943, "check_info": "Pulse width violation on signal clk", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 6, "hier": "tb.pipeline.fetch_unit.pipeline[23].cpu_top.branch_pred", "time_ns": 0.46215, "check_info": "Period violation on signal status", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:22"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 7, "hier": "tb.pipeline.cpu_top[5].memory_ctrl[26].writeback_unit.tlb", "time_ns": 8.156062, "check_info": "Skew violation on signal error", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 8, "hier": "tb.branch_pred.mmu[24].branch_pred.execute_unit.interrupt_ctrl", "time_ns": 8.16057, "check_info": "Setup time violation on signal data[2]", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 9, "hier": "tb.fetch_unit[25].tlb[15].cache_unit[0]", "time_ns": 4.434342, "check_info": "Hold time violation on signal cs", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:22"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 10, "hier": "tb.writeback_unit.branch_pred.decode_unit[21].pipeline", "time_ns": 9.917, "check_info": "Pulse width violation on signal mode", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 11, "hier": "tb.cache_unit.writeback_unit.execute_unit", "time_ns": 6.402, "check_info": "Recovery time violation on signal intr[51]", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 12, "hier": "tb.cpu_top.tlb.tlb.mmu[26].memory_ctrl[14].branch_pred", "time_ns": 7.045831, "check_info": "Pulse width violation on signal enable[9]", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 13, "hier": "tb.pcie_if.memory_ctrl.tlb.fetch_unit.debug_unit[1]", "time_ns": 1.470278, "check_info": "Removal time violation on signal error[60]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:23"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 14, "hier": "tb.interrupt_ctrl[18].debug_unit.interrupt_ctrl.pcie_if[9].mmu", "time_ns": 3.827036, "check_info": "Pulse width violation on signal rd_en", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:23"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 15, "hier": "tb.interrupt_ctrl.interrupt_ctrl.debug_unit[0].pipeline", "time_ns": 5.721, "check_info": "Period violation on signal mode", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 16, "hier": "tb.mmu.cache_unit[16].memory_ctrl", "time_ns": 2.466051, "check_info": "Setup time violation on signal ack", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:23"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 17, "hier": "tb.cache_unit.tlb.execute_unit[24].interrupt_ctrl.fetch_unit.decode_unit", "time_ns": 3.278704, "check_info": "Recovery time violation on signal error", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:23"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 18, "hier": "tb.interrupt_ctrl.pipeline.execute_unit.writeback_unit.tlb.debug_unit", "time_ns": 4.136, "check_info": "Hold time violation on signal valid", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:24"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 19, "hier": "tb.pipeline.debug_unit[15].decode_unit", "time_ns": 5.379671, "check_info": "Recovery time violation on signal busy[11]", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 20, "hier": "tb.cpu_top[2].execute_unit.interrupt_ctrl.fetch_unit[8].memory_ctrl.mmu", "time_ns": 3.265057, "check_info": "Setup time violation on signal idle", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:24"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 21, "hier": "tb.mmu.ddr_ctrl[20].mmu.ddr_ctrl.branch_pred.writeback_unit", "time_ns": 8.361996, "check_info": "Recovery time violation on signal clk", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 22, "hier": "tb.debug_unit.decode_unit.cache_unit[3].execute_unit[26]", "time_ns": 8.493, "check_info": "Period violation on signal grant", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 23, "hier": "tb.cpu_top.mmu.pcie_if[9].pcie_if.cpu_top.interrupt_ctrl", "time_ns": 1.037695, "check_info": "Removal time violation on signal ack", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:24"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 24, "hier": "tb.pcie_if.branch_pred.decode_unit.pipeline.interrupt_ctrl.branch_pred", "time_ns": 2.002711, "check_info": "Period violation on signal grant", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:24"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 25, "hier": "tb.execute_unit.cache_unit.fetch_unit.writeback_unit.memory_ctrl[2]", "time_ns": 2.046921, "check_info": "Period violation on signal ack", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:25"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 26, "hier": "tb.interrupt_ctrl[5].decode_unit.memory_ctrl.cpu_top[23].writeback_unit.decode_unit", "time_ns": 6.541, "check_info": "Hold time violation on signal valid[58]", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 27, "hier": "tb.branch_pred.writeback_unit.pcie_if.decode_unit[7]", "time_ns": 9.694065, "check_info": "Hold time violation on signal valid", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 28, "hier": "tb.debug_unit[5].debug_unit[7].ddr_ctrl", "time_ns": 2.214, "check_info": "Skew violation on signal enable", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:25"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 29, "hier": "tb.tlb.cache_unit.debug_unit.memory_ctrl", "time_ns": 7.341278, "check_info": "Removal time violation on signal error[45]", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 30, "hier": "tb.pipeline.decode_unit.fetch_unit.mmu[24].execute_unit.branch_pred[25]", "time_ns": 9.327144, "check_info": "Recovery time violation on signal grant[32]", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 31, "hier": "tb.cpu_top[4].execute_unit[2].mmu.writeback_unit.debug_unit", "time_ns": 8.64523, "check_info": "Pulse width violation on signal data[19]", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 32, "hier": "tb.tlb.ddr_ctrl.cache_unit", "time_ns": 6.016467, "check_info": "Setup time violation on signal rst_n[11]", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 33, "hier": "tb.interrupt_ctrl.tlb[5].tlb[13].fetch_unit[12].pipeline[12]", "time_ns": 8.285, "check_info": "Period violation on signal data", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 34, "hier": "tb.fetch_unit.writeback_unit.pipeline.cpu_top[1].mmu[24].pipeline", "time_ns": 5.608969, "check_info": "Hold time violation on signal mode", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 35, "hier": "tb.memory_ctrl.cache_unit.pipeline", "time_ns": 8.882245, "check_info": "Removal time violation on signal valid", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 36, "hier": "tb.fetch_unit.memory_ctrl.fetch_unit.interrupt_ctrl", "time_ns": 1.261, "check_info": "Pulse width violation on signal enable", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:25"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 37, "hier": "tb.fetch_unit[28].mmu.execute_unit[15].writeback_unit.debug_unit.interrupt_ctrl", "time_ns": 9.454, "check_info": "Period violation on signal clk[50]", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 38, "hier": "tb.fetch_unit.cache_unit[13].mmu.pipeline[4]", "time_ns": 9.652, "check_info": "Period violation on signal busy", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 39, "hier": "tb.mmu.branch_pred[7].cpu_top[0].branch_pred[20].cache_unit", "time_ns": 3.693453, "check_info": "Skew violation on signal mode[31]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:25"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 40, "hier": "tb.debug_unit[31].cache_unit.memory_ctrl.tlb", "time_ns": 9.463669, "check_info": "Setup time violation on signal rst_n[6]", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 41, "hier": "tb.fetch_unit[21].mmu.branch_pred.interrupt_ctrl.debug_unit", "time_ns": 3.813917, "check_info": "Removal time violation on signal valid[19]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:26"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 42, "hier": "tb.fetch_unit.writeback_unit[19].pipeline[6].cpu_top[10].decode_unit.ddr_ctrl", "time_ns": 2.978951, "check_info": "Period violation on signal addr", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:26"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 43, "hier": "tb.cache_unit.pcie_if.fetch_unit.execute_unit.ddr_ctrl", "time_ns": 6.754428, "check_info": "Pulse width violation on signal intr", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 44, "hier": "tb.decode_unit.cache_unit.cache_unit.pcie_if.cpu_top[18]", "time_ns": 2.993739, "check_info": "Removal time violation on signal intr[36]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:26"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 45, "hier": "tb.branch_pred.pcie_if.mmu[22].debug_unit", "time_ns": 6.441, "check_info": "Recovery time violation on signal mode[22]", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 46, "hier": "tb.pipeline[29].branch_pred.memory_ctrl[13]", "time_ns": 2.996531, "check_info": "Period violation on signal mode", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:26"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 47, "hier": "tb.ddr_ctrl.pcie_if[22].pipeline[18]", "time_ns": 5.938838, "check_info": "Setup time violation on signal addr", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 48, "hier": "tb.branch_pred.mmu.interrupt_ctrl.memory_ctrl[29]", "time_ns": 5.389, "check_info": "Removal time violation on signal ready", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 49, "hier": "tb.fetch_unit.branch_pred[11].execute_unit.interrupt_ctrl.pcie_if.pipeline[19]", "time_ns": 0.729587, "check_info": "Hold time violation on signal error[17]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:26"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 50, "hier": "tb.tlb[13].memory_ctrl.pipeline.mmu.decode_unit.memory_ctrl", "time_ns": 4.348884, "check_info": "Period violation on signal intr[13]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:27"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 51, "hier": "tb.writeback_unit.cpu_top.ddr_ctrl", "time_ns": 0.963, "check_info": "Period violation on signal rd_en[20]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:27"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 52, "hier": "tb.memory_ctrl[14].writeback_unit[1].memory_ctrl[20]", "time_ns": 2.703663, "check_info": "Skew violation on signal ready[2]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:27"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 53, "hier": "tb.tlb.interrupt_ctrl[6].memory_ctrl[1].memory_ctrl[5]", "time_ns": 4.739, "check_info": "Hold time violation on signal clk[50]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:27"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 54, "hier": "tb.decode_unit.fetch_unit.pipeline.tlb[16].decode_unit", "time_ns": 9.397971, "check_info": "Period violation on signal mode[33]", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 55, "hier": "tb.debug_unit.pcie_if.memory_ctrl.cache_unit.execute_unit.pcie_if", "time_ns": 3.245386, "check_info": "Hold time violation on signal valid", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:27"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 56, "hier": "tb.cpu_top.tlb[28].fetch_unit[17]", "time_ns": 2.231, "check_info": "Period violation on signal clk[3]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:28"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 57, "hier": "tb.tlb[17].ddr_ctrl[2].debug_unit.ddr_ctrl.ddr_ctrl", "time_ns": 6.875765, "check_info": "Removal time violation on signal rd_en[61]", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 58, "hier": "tb.mmu.ddr_ctrl.ddr_ctrl", "time_ns": 7.256, "check_info": "Setup time violation on signal rst_n", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 59, "hier": "tb.tlb[20].interrupt_ctrl[15].pcie_if.cache_unit.decode_unit.interrupt_ctrl", "time_ns": 5.785, "check_info": "Setup time violation on signal intr", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 60, "hier": "tb.branch_pred[3].memory_ctrl.decode_unit[7].mmu", "time_ns": 7.267576, "check_info": "Hold time violation on signal error", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 61, "hier": "tb.tlb[12].debug_unit[1].interrupt_ctrl[12].ddr_ctrl", "time_ns": 6.544, "check_info": "Setup time violation on signal error", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 62, "hier": "tb.interrupt_ctrl.branch_pred.cpu_top.cache_unit.tlb.writeback_unit", "time_ns": 8.155818, "check_info": "Skew violation on signal idle[19]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:28"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 63, "hier": "tb.branch_pred[7].interrupt_ctrl.cpu_top.tlb.writeback_unit.tlb", "time_ns": 1.678165, "check_info": "Period violation on signal grant[43]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:28"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 64, "hier": "tb.tlb[16].mmu.tlb.mmu[12]", "time_ns": 0.37622, "check_info": "Period violation on signal req", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:28"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 65, "hier": "tb.writeback_unit[27].pcie_if.debug_unit.ddr_ctrl", "time_ns": 4.864152, "check_info": "Hold time violation on signal error[1]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:29"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 66, "hier": "tb.fetch_unit.pcie_if.memory_ctrl.writeback_unit", "time_ns": 1.162898, "check_info": "Setup time violation on signal data", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:29"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 67, "hier": "tb.pipeline[9].branch_pred.tlb.pcie_if.execute_unit", "time_ns": 6.777464, "check_info": "Recovery time violation on signal ack", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:29"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 68, "hier": "tb.ddr_ctrl.cache_unit.cache_unit", "time_ns": 9.921402, "check_info": "Skew violation on signal enable", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:29"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 69, "hier": "tb.fetch_unit.cache_unit.cache_unit.tlb.pipeline.execute_unit", "time_ns": 9.795209, "check_info": "Hold time violation on signal clk", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:29"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 70, "hier": "tb.pipeline.fetch_unit.cpu_top[7].branch_pred.fetch_unit[25]", "time_ns": 9.242802, "check_info": "Period violation on signal addr[58]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:30"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 71, "hier": "tb.mmu.execute_unit.debug_unit", "time_ns": 4.371015, "check_info": "Setup time violation on signal clk[0]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:30"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 72, "hier": "tb.execute_unit.ddr_ctrl.tlb.mmu.cache_unit.branch_pred[17]", "time_ns": 4.761932, "check_info": "Hold time violation on signal addr[23]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:30"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 73, "hier": "tb.branch_pred.pcie_if[22].tlb.cache_unit.tlb[19].execute_unit", "time_ns": 4.709, "check_info": "Pulse width violation on signal data", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:30"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 74, "hier": "tb.pipeline.debug_unit[6].cache_unit.cache_unit.branch_pred[10]", "time_ns": 3.125558, "check_info": "Removal time violation on signal done[52]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:30"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 75, "hier": "tb.branch_pred[24].pipeline.tlb.ddr_ctrl[2].branch_pred", "time_ns": 5.228133, "check_info": "Pulse width violation on signal grant[19]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:31"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 76, "hier": "tb.pipeline[19].pcie_if[4].cache_unit.decode_unit[25].debug_unit[6]", "time_ns": 2.73, "check_info": "Pulse width violation on signal wr_en", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:31"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 77, "hier": "tb.debug_unit.fetch_unit.pipeline[21]", "time_ns": 6.835257, "check_info": "Setup time violation on signal busy[33]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:31"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 78, "hier": "tb.ddr_ctrl.writeback_unit[6].fetch_unit[15].writeback_unit[17]", "time_ns": 9.485334, "check_info": "Pulse width violation on signal wr_en[55]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:31"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 79, "hier": "tb.pipeline.branch_pred.tlb.memory_ctrl", "time_ns": 2.135298, "check_info": "Recovery time violation on signal valid[21]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:31"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 80, "hier": "tb.execute_unit.memory_ctrl[15].debug_unit.pipeline.mmu[13]", "time_ns": 4.531255, "check_info": "Skew violation on signal rst_n[24]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:32"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 81, "hier": "tb.decode_unit[18].pipeline.cache_unit[15]", "time_ns": 7.567369, "check_info": "Period violation on signal busy[47]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:32"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 82, "hier": "tb.interrupt_ctrl.execute_unit[20].ddr_ctrl[27].memory_ctrl.tlb", "time_ns": 5.923723, "check_info": "Period violation on signal error[24]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:32"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 83, "hier": "tb.cpu_top[30].pipeline.pcie_if.mmu.cache_unit[21].pipeline", "time_ns": 2.121, "check_info": "Period violation on signal wr_en[55]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:32"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 84, "hier": "tb.fetch_unit.writeback_unit.execute_unit.ddr_ctrl.debug_unit[13].cache_unit", "time_ns": 6.752, "check_info": "Pulse width violation on signal rst_n", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:32"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 85, "hier": "tb.interrupt_ctrl.tlb.debug_unit.pipeline.writeback_unit", "time_ns": 0.493199, "check_info": "Setup time violation on signal ready[11]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:33"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 86, "hier": "tb.interrupt_ctrl[17].mmu.cpu_top[24].tlb.memory_ctrl", "time_ns": 0.973166, "check_info": "Recovery time violation on signal req", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:33"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 87, "hier": "tb.writeback_unit.cache_unit.branch_pred.ddr_ctrl.branch_pred[5].decode_unit[6]", "time_ns": 5.328, "check_info": "Recovery time violation on signal mode[26]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:33"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 88, "hier": "tb.execute_unit[14].ddr_ctrl.cache_unit.branch_pred.cache_unit.debug_unit", "time_ns": 3.889423, "check_info": "Hold time violation on signal ack", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:33"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 89, "hier": "tb.debug_unit[13].ddr_ctrl.mmu", "time_ns": 1.874, "check_info": "Removal time violation on signal idle[27]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:34"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 90, "hier": "tb.memory_ctrl.debug_unit.interrupt_ctrl", "time_ns": 7.527944, "check_info": "Period violation on signal data[53]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:34"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 91, "hier": "tb.interrupt_ctrl.mmu.branch_pred[15].cache_unit[16].mmu", "time_ns": 6.159095, "check_info": "Removal time violation on signal wr_en", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:34"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 92, "hier": "tb.ddr_ctrl.cpu_top.fetch_unit.writeback_unit", "time_ns": 7.547946, "check_info": "Skew violation on signal valid[20]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:34"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 93, "hier": "tb.memory_ctrl.fetch_unit.writeback_unit.cache_unit.tlb.ddr_ctrl", "time_ns": 2.095826, "check_info": "Hold time violation on signal idle", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:34"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 94, "hier": "tb.pipeline[22].fetch_unit.decode_unit.execute_unit", "time_ns": 5.797086, "check_info": "Setup time violation on signal error", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:35"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 95, "hier": "tb.debug_unit.writeback_unit.pipeline", "time_ns": 6.902551, "check_info": "Setup time violation on signal grant[2]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:35"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 96, "hier": "tb.decode_unit[20].cpu_top.mmu.debug_unit", "time_ns": 5.94, "check_info": "Pulse width violation on signal intr[24]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:35"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 97, "hier": "tb.tlb.branch_pred.mmu[0].memory_ctrl", "time_ns": 3.282533, "check_info": "Skew violation on signal valid", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:36"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 98, "hier": "tb.tlb[31].decode_unit.cpu_top.cache_unit.debug_unit.writeback_unit", "time_ns": 7.714725, "check_info": "Pulse width violation on signal intr[56]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:36"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 99, "hier": "tb.decode_unit.fetch_unit[13].cache_unit.pipeline.debug_unit", "time_ns": 2.945, "check_info": "Skew violation on signal idle[52]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:36"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 100, "hier": "tb.decode_unit.tlb.debug_unit.branch_pred", "time_ns": 1.634876, "check_info": "Period violation on signal data[30]", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:36"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 101, "hier": "tb.cpu_top.fetch_unit[13].interrupt_ctrl.pcie_if[26]", "time_ns": 866.002233, "check_info": "setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:7869100 FS, negedge SE:7735500 FS,0.1621 : 262100FS, -0.0241 : -24100 FS )", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 102, "hier": "tb.cpu_top.fetch_unit[13].interrupt_ctrl.pcie_if[27]", "time_ns": 866.002234, "check_info": "setuphold<hold>( negedge cpN && nCD nD SI SDFCHK:7869100 FS, negedge SE:7735500 FS,0.1621 : 262100FS, -0.0241 : -24100 FS )", "status": "已确认", "confirmer": "bbb", "result": "通过", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03"}, {"corner": "npg_f2_ffg", "case": "page_test", "source": "excel", "num": 103, "hier": "tb.cpu_top.fetch_unit[13].interrupt_ctrl.pcie_if[28]", "time_ns": 866.002234, "check_info": "setup( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )", "status": "已确认", "confirmer": "jiadong.he2", "result": "通过", "reason": "test", "confirmed_at": "2025-08-07 16:10:37"}]