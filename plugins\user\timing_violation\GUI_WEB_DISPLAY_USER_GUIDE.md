# GUI网页展示功能 - 用户指南

## 🎯 功能概述

现在你可以直接在GUI界面中点击按钮，一键启动时序违例的网页展示功能！无需手动执行Python命令，系统会自动完成所有操作。

## 🚀 一键操作指南

### 步骤1：打开GUI
启动时序违例确认工具的主界面。

### 步骤2：点击"网页显示"按钮
在工具栏中找到"网页显示"按钮，点击即可。

### 步骤3：等待自动处理
系统会自动执行以下操作：
- ✅ 从数据库获取当前所有违例数据
- ✅ 按corner和case_name进行智能分组
- ✅ 生成优化的网页数据文件
- ✅ 启动本地Web服务器
- ✅ 自动打开浏览器显示结果

### 步骤4：查看结果
浏览器会自动打开并显示：
- 📊 **统计概览** - 总违例数、确认数、Corner数、Case数
- 🔍 **智能过滤** - 按Corner、Case、状态进行过滤
- 📋 **详细表格** - 完整的违例信息展示
- 📱 **响应式界面** - 支持不同设备访问

## 🎛️ 界面功能

### 主要功能区域

#### 1. 统计卡片
- **总违例数** - 显示所有违例的总数
- **Corner数** - 显示涉及的工艺角数量
- **Case数** - 显示涉及的测试用例数量
- **已确认** - 显示已确认的违例数量

#### 2. 过滤器
- **Corner过滤** - 选择特定的工艺角
- **Case过滤** - 选择特定的测试用例
- **状态过滤** - 筛选确认状态

#### 3. 数据表格
显示详细的违例信息：
- 序号、层级路径、时间、检查信息
- 状态、确认人、结果、理由、确认时间
- Corner、Case信息

### 按钮功能

#### "网页显示"按钮
- **首次点击** - 启动网页展示功能
- **再次点击** - 刷新数据并重新打开浏览器

#### "停止网页服务"按钮
- 点击后停止Web服务器
- 恢复"网页显示"按钮状态

## 🔧 技术特性

### 数据库优先策略
- ✅ 优先从SQLite数据库读取已确认的违例数据
- ✅ 智能数据组织，按corner和case_name分组
- ✅ Excel文件作为备选数据源
- ✅ 自动数据验证和清理

### 性能优化
- ⚡ 支持大数据集的高效处理
- 🗜️ 自动数据压缩和分页
- 💾 智能缓存机制
- 🔄 虚拟滚动支持

### 用户体验
- 🖱️ 一键操作，无需命令行
- 📱 响应式设计，支持多设备
- 🔍 实时过滤和搜索
- 📊 直观的统计展示

## 🛠️ 故障排除

### 问题1：按钮点击无响应
**可能原因**: 缺少必要的模块或文件
**解决方案**:
1. 检查控制台错误信息
2. 确保所有web_display模块文件完整
3. 重启GUI应用程序

### 问题2：数据显示为空
**可能原因**: 数据库中没有确认的违例数据
**解决方案**:
1. 确认数据库中有已确认的违例记录
2. 检查数据库连接是否正常
3. 尝试重新确认一些违例后再次点击

### 问题3：浏览器没有自动打开
**可能原因**: 系统浏览器设置问题
**解决方案**:
1. 查看GUI状态栏的提示信息
2. 手动访问显示的URL地址
3. 通常是: http://localhost:8000/standalone_test.html

### 问题4：端口被占用
**可能原因**: 8000端口被其他程序占用
**解决方案**:
1. 系统会自动寻找可用端口
2. 查看状态栏显示的实际访问地址
3. 如果问题持续，重启GUI程序

## 📊 数据展示说明

### 数据来源
- **主要来源**: SQLite数据库中的确认记录
- **数据组织**: 按corner和case_name智能分组
- **实时性**: 反映当前数据库中的最新状态

### 显示内容
- **完整信息**: 包含所有违例的详细信息
- **确认状态**: 显示确认人、结果、理由等
- **时间信息**: 显示确认时间和违例时间
- **分组信息**: 清晰的corner和case分类

### 过滤功能
- **多级过滤**: 支持corner、case、状态的组合过滤
- **实时更新**: 过滤结果实时显示
- **统计同步**: 过滤后统计数据自动更新

## 🎯 使用技巧

### 高效操作
1. **首次使用** - 点击"网页显示"按钮，等待自动处理
2. **数据更新** - 确认新违例后，再次点击按钮刷新
3. **多角度查看** - 使用过滤器从不同角度分析数据
4. **详细检查** - 点击表格行查看完整信息

### 最佳实践
1. **定期刷新** - 在确认新违例后及时刷新网页数据
2. **合理过滤** - 使用过滤器聚焦关注的corner或case
3. **状态跟踪** - 通过状态过滤查看确认进度
4. **数据导出** - 可以从浏览器直接打印或保存页面

## 🔄 更新和维护

### 数据同步
- 网页数据与数据库实时同步
- 每次点击按钮都会获取最新数据
- 支持增量更新和全量刷新

### 版本兼容
- 兼容现有的数据库结构
- 支持Excel文件备选方案
- 向后兼容旧版本数据

## 📞 技术支持

### 日志查看
- GUI控制台会显示详细的操作日志
- 浏览器开发者工具可查看前端日志
- 系统会生成详细的错误信息

### 常见问题
- 大部分问题可通过重新点击按钮解决
- 持续问题请检查数据库完整性
- 网络问题请检查防火墙设置

---

## 🎉 开始使用

**最简单的使用方式**:
1. 打开时序违例确认工具GUI
2. 点击工具栏中的"网页显示"按钮
3. 等待浏览器自动打开
4. 享受优化的网页展示体验！

现在你可以轻松地通过GUI一键启动网页展示功能，无需任何命令行操作！