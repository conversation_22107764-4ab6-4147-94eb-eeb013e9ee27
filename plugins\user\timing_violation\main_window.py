"""
时序违例确认插件主窗口

提供时序违例确认的主要用户界面。
"""

import os
import sys
import time
from typing import List, Dict, Optional
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QComboBox, QTableWidget, QTableWidgetItem,
    QFileDialog, QMessageBox, QProgressBar, QGroupBox, QHeaderView,
    QStatusBar, QToolBar, QAction, QCheckBox, QTextEdit, QDialog,
    QDialogButtonBox, QFormLayout, QRadioButton, QButtonGroup, QApplication,
    QAbstractItemView, QScrollBar, QFrame, QScrollArea, QListWidget, QListWidgetItem
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QMetaObject, Q_ARG, QAbstractTableModel, QModelIndex, QVariant, QThread
from PyQt5.QtGui import QFont, QColor, QIcon

# 临时禁用QMessageBox以防止分页时的弹窗问题
class SafeMessageBox:
    """安全的消息框类，避免在分页时出现弹窗"""

    @staticmethod
    def warning(parent, title, message):
        print(f"警告: {title} - {message}")
        return None

    @staticmethod
    def critical(parent, title, message):
        print(f"错误: {title} - {message}")
        return None

    @staticmethod
    def information(parent, title, message):
        print(f"信息: {title} - {message}")
        return None

    @staticmethod
    def question(parent, title, message, buttons=None):
        print(f"询问: {title} - {message}")
        # 默认返回Yes，避免阻塞
        return QMessageBox.Yes if hasattr(QMessageBox, 'Yes') else 16384

# 在分页操作期间使用安全的消息框
_original_messagebox = QMessageBox
_use_safe_messagebox = False

def enable_safe_messagebox():
    """启用安全消息框模式"""
    global _use_safe_messagebox
    _use_safe_messagebox = True

def disable_safe_messagebox():
    """禁用安全消息框模式"""
    global _use_safe_messagebox
    _use_safe_messagebox = False

def get_messagebox():
    """获取当前应该使用的消息框类"""
    return SafeMessageBox if _use_safe_messagebox else _original_messagebox

# 全局替换QMessageBox，确保在分页期间使用安全版本
def safe_messagebox_wrapper(original_method):
    """安全消息框包装器"""
    def wrapper(*args, **kwargs):
        if _use_safe_messagebox:
            # 在安全模式下，将所有消息框调用转为控制台输出
            method_name = original_method.__name__
            if len(args) >= 3:
                parent, title, message = args[0], args[1], args[2]
                print(f"[{method_name}] {title}: {message}")
            return None
        else:
            return original_method(*args, **kwargs)
    return wrapper

# 动态包装QMessageBox的静态方法
_original_warning = QMessageBox.warning
_original_critical = QMessageBox.critical
_original_information = QMessageBox.information
_original_question = QMessageBox.question

QMessageBox.warning = safe_messagebox_wrapper(_original_warning)
QMessageBox.critical = safe_messagebox_wrapper(_original_critical)
QMessageBox.information = safe_messagebox_wrapper(_original_information)
QMessageBox.question = safe_messagebox_wrapper(_original_question)

from plugins.base import NonModalDialog
from .models import ViolationDataModel
from .parser import VioLogParser, AsyncVioLogParser, HighPerformanceVioLogParser, HighPerformanceAsyncParser, CaseInfoParser
from .performance_optimizer import PerformanceOptimizer
from .strategy_manager import IntelligentStrategyManager
from .regression_batch_ui import RegressionBatchDialog
from .regression_scanner import RegressionFileInfo
from utils.batch_operations_manager import ViolationBatchProcessor, BatchOperationProgress
from utils.batch_progress_dialog import BatchProgressDialog, SimpleBatchProgressDialog

# Performance Integration Components
try:
    from .main_window_integration import MainWindowIntegrationAdapter, BackwardCompatibilityManager
    PERFORMANCE_INTEGRATION_AVAILABLE = True
except ImportError as e:
    try:
        from main_window_integration import MainWindowIntegrationAdapter, BackwardCompatibilityManager
        PERFORMANCE_INTEGRATION_AVAILABLE = True
    except ImportError as e2:
        print(f"Performance integration not available: {e2}")
        MainWindowIntegrationAdapter = None
        BackwardCompatibilityManager = None
        PERFORMANCE_INTEGRATION_AVAILABLE = False


class PatternSaveThread(QThread):
    """后台保存历史模式的线程"""
    save_completed = pyqtSignal(int)  # 保存完成，参数为保存的模式数量
    save_failed = pyqtSignal(str)     # 保存失败，参数为错误信息

    def __init__(self, violations, confirmation_data, data_model):
        super().__init__()
        self.violations = violations
        self.confirmation_data = confirmation_data
        self.data_model = data_model

    def run(self):
        """在后台线程中执行历史模式保存"""
        try:
            pattern_count = self._batch_save_patterns_optimized()
            self.save_completed.emit(pattern_count)
        except Exception as e:
            self.save_failed.emit(str(e))

    def _batch_save_patterns_optimized(self):
        """优化的批量保存历史模式方法（后台线程版本）"""
        try:
            total_count = len(self.violations)

            if total_count <= 1000:
                return self._save_patterns_normal()
            elif total_count <= 5000:
                return self._save_patterns_sampled(sample_rate=0.3)
            else:
                return self._save_patterns_representative(max_patterns=100)

        except Exception as e:
            print(f"后台批量保存历史模式失败: {e}")
            return 0

    def _save_patterns_normal(self):
        """正常模式：保存所有唯一模式"""
        pattern_count = 0
        unique_patterns = set()

        for violation in self.violations:
            if violation.get('is_group_header', False):
                continue  # 跳过分组标识行

            hier = violation.get('hier', '')
            check_info = violation.get('check_info', '')
            pattern_key = (hier, check_info)

            if pattern_key not in unique_patterns:
                unique_patterns.add(pattern_key)
                try:
                    self.data_model.save_pattern(
                        hier,
                        check_info,
                        self.confirmation_data['confirmer'],
                        self.confirmation_data['result'],
                        self.confirmation_data['reason']
                    )
                    pattern_count += 1
                except Exception:
                    pass

        return pattern_count

    def _save_patterns_sampled(self, sample_rate=0.3):
        """采样模式：按比例采样保存模式"""
        import random

        # 先去重获取唯一模式
        unique_patterns = {}
        for violation in self.violations:
            if violation.get('is_group_header', False):
                continue  # 跳过分组标识行

            hier = violation.get('hier', '')
            check_info = violation.get('check_info', '')
            pattern_key = (hier, check_info)

            if pattern_key not in unique_patterns:
                unique_patterns[pattern_key] = violation

        # 按采样率选择模式
        pattern_list = list(unique_patterns.items())
        sample_size = max(1, int(len(pattern_list) * sample_rate))
        sampled_patterns = random.sample(pattern_list, min(sample_size, len(pattern_list)))

        pattern_count = 0
        for pattern_key, violation in sampled_patterns:
            try:
                self.data_model.save_pattern(
                    violation.get('hier', ''),
                    violation.get('check_info', ''),
                    self.confirmation_data['confirmer'],
                    self.confirmation_data['result'],
                    self.confirmation_data['reason']
                )
                pattern_count += 1
            except Exception:
                pass

        print(f"后台采样模式：从 {len(unique_patterns)} 个唯一模式中采样 {len(sampled_patterns)} 个")
        return pattern_count

    def _save_patterns_representative(self, max_patterns=100):
        """代表性模式：只保存最具代表性的模式"""
        # 统计模式频率
        pattern_frequency = {}
        for violation in self.violations:
            if violation.get('is_group_header', False):
                continue  # 跳过分组标识行

            hier = violation.get('hier', '')
            check_info = violation.get('check_info', '')
            pattern_key = (hier, check_info)

            if pattern_key not in pattern_frequency:
                pattern_frequency[pattern_key] = {'count': 0, 'violation': violation}
            pattern_frequency[pattern_key]['count'] += 1

        # 按频率排序，选择最常见的模式
        sorted_patterns = sorted(
            pattern_frequency.items(),
            key=lambda x: x[1]['count'],
            reverse=True
        )

        # 只保存前max_patterns个最常见的模式
        top_patterns = sorted_patterns[:max_patterns]

        pattern_count = 0
        for pattern_key, pattern_data in top_patterns:
            violation = pattern_data['violation']
            try:
                self.data_model.save_pattern(
                    violation.get('hier', ''),
                    violation.get('check_info', ''),
                    self.confirmation_data['confirmer'],
                    self.confirmation_data['result'],
                    self.confirmation_data['reason']
                )
                pattern_count += 1
            except Exception:
                pass

        print(f"后台代表性模式：从 {len(pattern_frequency)} 个唯一模式中选择 {len(top_patterns)} 个最常见的模式")
        return pattern_count


class ViolationTableModel(QAbstractTableModel):
    """高性能违例表格数据模型，支持大数据集虚拟滚动"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self._data = []
        self._headers = ["NUM", "层级路径", "时间(ns)", "检查信息", "状态", "确认人", "确认结果", "操作"]
        self._parent_window = parent

    def rowCount(self, parent=QModelIndex()):
        return len(self._data)

    def columnCount(self, parent=QModelIndex()):
        return len(self._headers)

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if orientation == Qt.Horizontal and role == Qt.DisplayRole:
            return self._headers[section]
        return QVariant()

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid() or index.row() >= len(self._data):
            return QVariant()

        violation = self._data[index.row()]
        column = index.column()

        if role == Qt.DisplayRole:
            return self._get_display_data(violation, column, index.row())
        elif role == Qt.ForegroundRole:
            return self._get_foreground_color(violation)
        elif role == Qt.BackgroundRole and column == 4:  # 状态列
            return self._get_status_background_color(violation)
        elif role == Qt.TextAlignmentRole:
            if column in [0, 2, 4, 5, 6]:  # NUM, 时间, 状态, 确认人, 确认结果
                return Qt.AlignCenter
            return Qt.AlignLeft | Qt.AlignVCenter
        elif role == Qt.ToolTipRole:
            if column == 1:  # 层级路径
                return violation.get('hier', '')
            elif column == 3:  # 检查信息
                return violation.get('check_info', '')

        return QVariant()

    def _get_display_data(self, violation, column, row_index=None):
        """获取显示数据"""
        if column == 0:  # NUM
            # 使用全局连续编号：当前页 * 页面大小 + 行索引 + 1
            # 这里需要通过父窗口获取页面信息
            if row_index is not None and self._parent_window:
                try:
                    current_page = getattr(self._parent_window, 'current_page', 0)
                    page_size = getattr(self._parent_window, 'page_size', 100)
                    global_num = current_page * page_size + row_index + 1
                    return str(global_num)
                except:
                    pass
            # 回退：如果无法获取页面信息，使用行索引+1
            return str((row_index or 0) + 1)
        elif column == 1:  # 层级路径
            return violation.get('hier', '')
        elif column == 2:  # 时间(ns)
            # 优先使用数据库中存储的格式化时间显示，确保与对话框显示一致
            time_display = violation.get('time_display', '')
            if time_display:
                # 如果有存储的时间显示格式，直接使用
                if time_display.endswith('FS'):
                    time_ns = float(time_display[:-3]) / 1000000
                elif time_display.endswith('PS'):
                    time_ns = float(time_display[:-3]) / 1000
                else:
                    time_ns = float(time_display[:-3])
                return f"{time_ns:.3f}"
            else:
                # 回退到计算方式（兼容旧数据）
                time_fs = violation.get('time_fs', 0)
                time_ns = time_fs / 1000000 if time_fs else 0
                return f"{time_ns:.3f}"
        elif column == 3:  # 检查信息
            return violation.get('check_info', '')
        elif column == 4:  # 状态
            status = violation.get('status', 'pending')
            return self._get_status_display(status)
        elif column == 5:  # 确认人
            return violation.get('confirmer', '')
        elif column == 6:  # 确认结果
            result = violation.get('result', '')
            return self._get_result_display(result)
        elif column == 7:  # 操作
            status = violation.get('status', 'pending')
            return "确认" if status == 'pending' else "编辑"

        return ""

    def _get_foreground_color(self, violation):
        """获取前景色"""
        status = violation.get('status', 'pending')
        if status in ['confirmed', 'ignored']:
            return QColor(128, 128, 128)  # 灰色
        return QColor(0, 0, 0)  # 黑色

    def _get_status_background_color(self, violation):
        """获取状态列背景色"""
        status = violation.get('status', 'pending')
        if status == 'confirmed':
            return QColor(144, 238, 144)  # 浅绿色
        elif status == 'ignored':
            return QColor(255, 182, 193)  # 浅红色
        else:
            return QColor(255, 255, 224)  # 浅黄色

    def _get_status_display(self, status):
        """获取状态显示文本"""
        status_map = {
            'pending': '待确认',
            'confirmed': '已确认',
            'ignored': '已忽略'
        }
        return status_map.get(status, status)

    def _get_result_display(self, result):
        """获取结果显示文本"""
        result_map = {
            'pass': '通过',
            'issue': '有问题',
            '': ''
        }
        return result_map.get(result, result)

    def update_data(self, violations):
        """更新数据"""
        self.beginResetModel()
        self._data = violations
        self.endResetModel()

    def get_violation_at_row(self, row):
        """获取指定行的违例数据"""
        if 0 <= row < len(self._data):
            return self._data[row]
        return None


class HighPerformanceTableView(QWidget):
    """高性能表格视图，支持大数据集和虚拟滚动"""

    # 信号定义
    cell_double_clicked = pyqtSignal(int, int)  # 行, 列
    action_button_clicked = pyqtSignal(object, str)  # violation_id（支持字符串和整数）, 动作类型

    def __init__(self, parent=None):
        super().__init__(parent)
        self.model = ViolationTableModel(parent)
        self.init_ui()

        # 分页参数 - 优化显示数量，提升用户体验
        self.page_size = 150    # 增加每页显示数量，提升用户体验
        self.current_page = 0   # 当前页码（从0开始）
        self.total_pages = 0    # 总页数
        self.row_height = 35    # 行高，与标准模式保持一致

        # 缓存的按钮和性能优化
        self.button_cache = {}
        self.widget_pool = []   # 控件对象池，复用控件减少创建开销
        self.max_pool_size = 200  # 控件池最大大小

        # 选中状态管理 - 支持批量确认功能
        self.selected_violations = {}  # {violation_id: True/False} 全局选中状态
        self.select_all_state = False  # 全选状态
        self.checkbox_cache = {}  # 复选框缓存，提升性能
        
        # Enhanced pagination features for large datasets (>20K violations)
        self.violation_count = 0
        self.violation_complexity_cache = {}  # Cache for violation complexity analysis
        self.prefetch_cache = {}  # Cache for background prefetching
        self.prefetch_timer = QTimer()
        self.prefetch_timer.timeout.connect(self._background_prefetch)
        self.prefetch_timer.setSingleShot(True)
        
        # Violation-aware page sizing - 优化显示数量
        self.adaptive_page_sizing = True
        self.min_page_size = 50   # 提高最小页面大小
        self.max_page_size = 300  # 提高最大页面大小
        self.base_page_size = 150 # 提高基础页面大小
        
        # Lazy loading and memory management
        self.lazy_loading_enabled = True
        self.memory_cleanup_timer = QTimer()
        self.memory_cleanup_timer.timeout.connect(self._cleanup_memory)
        self.memory_cleanup_timer.setSingleShot(True)
        self.last_page_access_time = {}  # Track page access times for memory cleanup

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 创建表头
        self.header_widget = self.create_header()
        layout.addWidget(self.header_widget)

        # 创建分页控件
        self.pagination_widget = self.create_pagination_controls()
        layout.addWidget(self.pagination_widget)

        # 创建滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setFrameStyle(QFrame.Box)
        self.scroll_area.setLineWidth(1)
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        layout.addWidget(self.scroll_area)

        # 创建内容控件
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(0)

        # 将内容控件设置到滚动区域
        self.scroll_area.setWidget(self.content_widget)

        # 初始化行容器
        self.row_widgets = []

    def create_header(self):
        """创建表头"""
        header_widget = QWidget()
        header_widget.setFixedHeight(30)
        header_widget.setStyleSheet("""
            QWidget {
                background-color: #f8f8f8;
                border-bottom: 1px solid #d0d0d0;
                font-weight: bold;
                font-family: "Microsoft YaHei";
            }
        """)

        layout = QHBoxLayout(header_widget)
        layout.setContentsMargins(5, 0, 5, 0)
        layout.setSpacing(0)

        # 列宽配置 - 添加复选框列
        column_widths = [40, 60, 300, 100, 200, 80, 100, 80, 100]
        headers = ["选择", "NUM", "层级路径", "时间(ns)", "检查信息", "状态", "确认人", "确认结果", "操作"]

        for i, (header, width) in enumerate(zip(headers, column_widths)):
            if i == 0:  # 复选框列
                # 创建全选复选框
                checkbox_container = QWidget()
                checkbox_container.setFixedWidth(width)
                checkbox_layout = QHBoxLayout(checkbox_container)
                checkbox_layout.setContentsMargins(0, 0, 0, 0)
                checkbox_layout.setAlignment(Qt.AlignCenter)

                self.select_all_checkbox = QCheckBox()
                self.select_all_checkbox.setToolTip("全选/取消全选")
                self.select_all_checkbox.stateChanged.connect(self.on_select_all_changed)
                checkbox_layout.addWidget(self.select_all_checkbox)

                checkbox_container.setStyleSheet("border-right: 1px solid #d0d0d0; padding: 5px;")
                layout.addWidget(checkbox_container)
            else:
                label = QLabel(header)
                label.setFixedWidth(width)
                label.setAlignment(Qt.AlignCenter)
                label.setStyleSheet("border-right: 1px solid #d0d0d0; padding: 5px;")
                layout.addWidget(label)

        return header_widget

    def on_select_all_changed(self, state):
        """处理全选复选框状态变化"""
        try:
            self.select_all_state = (state == Qt.Checked)

            # 获取当前页面的所有违例
            start_index = self.current_page * self.page_size
            end_index = min(start_index + self.page_size, self.model.rowCount())

            # 更新当前页面所有违例的选中状态
            for row in range(start_index, end_index):
                violation = self.model.get_violation_at_row(row)
                if violation and not violation.get('is_group_header', False):
                    violation_id = violation.get('id')
                    if violation_id:
                        self.selected_violations[violation_id] = self.select_all_state

            # 只更新当前页面的复选框状态，不刷新整个页面
            self._update_current_page_checkboxes()

        except Exception as e:
            print(f"处理全选状态变化失败: {e}")

    def on_row_checkbox_changed(self, violation_id, state):
        """处理单行复选框状态变化"""
        try:
            if violation_id:
                self.selected_violations[violation_id] = (state == Qt.Checked)

                # 只更新全选复选框状态，不刷新页面
                self._update_select_all_checkbox_state_only()

        except Exception as e:
            print(f"处理行复选框状态变化失败: {e}")

    def update_select_all_checkbox_state(self):
        """更新全选复选框状态"""
        try:
            # 获取当前页面的所有可选择违例
            start_index = self.current_page * self.page_size
            end_index = min(start_index + self.page_size, self.model.rowCount())

            selectable_violations = []
            selected_count = 0

            for row in range(start_index, end_index):
                violation = self.model.get_violation_at_row(row)
                if violation and not violation.get('is_group_header', False):
                    violation_id = violation.get('id')
                    if violation_id:
                        selectable_violations.append(violation_id)
                        if self.selected_violations.get(violation_id, False):
                            selected_count += 1

            # 更新全选复选框状态
            if len(selectable_violations) == 0:
                self.select_all_checkbox.setCheckState(Qt.Unchecked)
            elif selected_count == len(selectable_violations):
                self.select_all_checkbox.setCheckState(Qt.Checked)
            elif selected_count > 0:
                self.select_all_checkbox.setCheckState(Qt.PartiallyChecked)
            else:
                self.select_all_checkbox.setCheckState(Qt.Unchecked)

        except Exception as e:
            print(f"更新全选复选框状态失败: {e}")

    def _update_select_all_checkbox_state_only(self):
        """只更新全选复选框状态，不触发页面刷新"""
        try:
            # 获取当前页面的所有可选择违例
            start_index = self.current_page * self.page_size
            end_index = min(start_index + self.page_size, self.model.rowCount())

            selectable_violations = []
            selected_count = 0

            for row in range(start_index, end_index):
                violation = self.model.get_violation_at_row(row)
                if violation and not violation.get('is_group_header', False):
                    violation_id = violation.get('id')
                    if violation_id:
                        selectable_violations.append(violation_id)
                        if self.selected_violations.get(violation_id, False):
                            selected_count += 1

            # 更新全选复选框状态（阻止信号发射避免递归）
            if hasattr(self, 'select_all_checkbox'):
                self.select_all_checkbox.blockSignals(True)
                if len(selectable_violations) == 0:
                    self.select_all_checkbox.setCheckState(Qt.Unchecked)
                elif selected_count == len(selectable_violations):
                    self.select_all_checkbox.setCheckState(Qt.Checked)
                elif selected_count > 0:
                    self.select_all_checkbox.setCheckState(Qt.PartiallyChecked)
                else:
                    self.select_all_checkbox.setCheckState(Qt.Unchecked)
                self.select_all_checkbox.blockSignals(False)

        except Exception as e:
            print(f"更新全选复选框状态失败: {e}")

    def _update_current_page_checkboxes(self):
        """更新当前页面所有复选框的状态，不刷新页面"""
        try:
            # 遍历当前页面的所有行控件
            for row_widget in self.row_widgets:
                if row_widget and row_widget.layout():
                    # 查找复选框（第一个控件）
                    first_item = row_widget.layout().itemAt(0)
                    if first_item and first_item.widget():
                        checkbox_container = first_item.widget()
                        if checkbox_container.layout():
                            checkbox_item = checkbox_container.layout().itemAt(0)
                            if checkbox_item and checkbox_item.widget():
                                checkbox = checkbox_item.widget()
                                if isinstance(checkbox, QCheckBox):
                                    # 从行控件中获取violation_id
                                    violation_id = self._get_violation_id_from_row_widget(row_widget)
                                    if violation_id:
                                        # 阻止信号发射，直接更新状态
                                        checkbox.blockSignals(True)
                                        checkbox.setChecked(self.selected_violations.get(violation_id, False))
                                        checkbox.blockSignals(False)

            # 更新全选复选框状态
            self._update_select_all_checkbox_state_only()

        except Exception as e:
            print(f"更新当前页面复选框状态失败: {e}")

    def _get_violation_id_from_row_widget(self, row_widget):
        """从行控件中获取violation_id"""
        try:
            # 首先尝试从复选框容器的属性中获取
            if row_widget and row_widget.layout():
                first_item = row_widget.layout().itemAt(0)
                if first_item and first_item.widget():
                    checkbox_container = first_item.widget()
                    violation_id = checkbox_container.property('violation_id')
                    if violation_id:
                        return violation_id

            # 回退方法：通过行控件在列表中的位置来计算
            if row_widget in self.row_widgets:
                row_index = self.row_widgets.index(row_widget)
                absolute_row = self.current_page * self.page_size + row_index
                violation = self.model.get_violation_at_row(absolute_row)
                if violation:
                    return violation.get('id')
            return None
        except Exception as e:
            print(f"获取violation_id失败: {e}")
            return None

    def create_pagination_controls(self):
        """创建分页控件"""
        pagination_widget = QWidget()
        pagination_widget.setFixedHeight(40)
        pagination_widget.setStyleSheet("""
            QWidget {
                background-color: #f0f0f0;
                border-bottom: 1px solid #d0d0d0;
            }
            QPushButton {
                background-color: #4a9eff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #3d8ced;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
        """)

        layout = QHBoxLayout(pagination_widget)
        layout.setContentsMargins(10, 5, 10, 5)
        layout.setSpacing(10)

        # 首页按钮
        self.first_page_btn = QPushButton("首页")
        self.first_page_btn.clicked.connect(self.go_to_first_page)
        layout.addWidget(self.first_page_btn)

        # 上一页按钮
        self.prev_page_btn = QPushButton("上一页")
        self.prev_page_btn.clicked.connect(self.go_to_prev_page)
        layout.addWidget(self.prev_page_btn)

        # 页码信息
        self.page_info_label = QLabel("第 1 页，共 1 页")
        layout.addWidget(self.page_info_label)

        # 记录信息
        self.record_info_label = QLabel("显示 0-0 条，共 0 条记录")
        layout.addWidget(self.record_info_label)

        # 弹性空间
        layout.addStretch()

        # 下一页按钮
        self.next_page_btn = QPushButton("下一页")
        self.next_page_btn.clicked.connect(self.go_to_next_page)
        layout.addWidget(self.next_page_btn)

        # 末页按钮
        self.last_page_btn = QPushButton("末页")
        self.last_page_btn.clicked.connect(self.go_to_last_page)
        layout.addWidget(self.last_page_btn)

        # 跳转到指定页（仅在大数据集时显示）
        self.jump_to_page_widget = QWidget()
        jump_layout = QHBoxLayout(self.jump_to_page_widget)
        jump_layout.setContentsMargins(0, 0, 0, 0)
        jump_layout.setSpacing(5)
        
        jump_layout.addWidget(QLabel("跳转到:"))
        self.page_input = QLineEdit()
        self.page_input.setFixedWidth(60)
        self.page_input.setPlaceholderText("页码")
        self.page_input.returnPressed.connect(self.jump_to_page)
        jump_layout.addWidget(self.page_input)
        
        self.jump_btn = QPushButton("跳转")
        self.jump_btn.clicked.connect(self.jump_to_page)
        jump_layout.addWidget(self.jump_btn)
        
        layout.addWidget(self.jump_to_page_widget)
        self.jump_to_page_widget.setVisible(False)  # Hidden by default

        return pagination_widget

    def update_data(self, violations):
        """更新数据（安全版本）"""
        try:
            enable_safe_messagebox()  # 防止数据更新过程中的弹窗

            # 清空选中状态（数据更新时）
            self.selected_violations.clear()
            self.select_all_state = False

            self.model.update_data(violations)
            self.violation_count = len(violations) if violations else 0

            # Apply violation-aware page sizing for large datasets
            if self.violation_count > 20000:
                self._optimize_page_size_for_large_dataset()

            self.current_page = 0  # 重置到第一页
            self.calculate_pagination()
            self.update_pagination_controls()
            self.refresh_current_page()

            # 重置全选复选框状态
            if hasattr(self, 'select_all_checkbox'):
                self.select_all_checkbox.setCheckState(Qt.Unchecked)

            # Start background prefetching for large datasets
            if self.violation_count > 5000:
                self._start_background_prefetching()

        except Exception as e:
            print(f"更新数据失败: {e}")
        finally:
            disable_safe_messagebox()

    def calculate_pagination(self):
        """计算分页信息"""
        total_records = self.model.rowCount()
        self.total_pages = (total_records + self.page_size - 1) // self.page_size if total_records > 0 else 1

        # 确保当前页在有效范围内
        if self.current_page >= self.total_pages:
            self.current_page = max(0, self.total_pages - 1)

    def update_pagination_controls(self):
        """更新分页控件状态"""
        total_records = self.model.rowCount()

        # 更新页码信息
        self.page_info_label.setText(f"第 {self.current_page + 1} 页，共 {self.total_pages} 页")

        # 计算当前页显示的记录范围
        start_record = self.current_page * self.page_size + 1
        end_record = min((self.current_page + 1) * self.page_size, total_records)

        if total_records > 0:
            self.record_info_label.setText(f"显示 {start_record}-{end_record} 条，共 {total_records} 条记录")
        else:
            self.record_info_label.setText("显示 0-0 条，共 0 条记录")

        # 更新按钮状态
        self.first_page_btn.setEnabled(self.current_page > 0)
        self.prev_page_btn.setEnabled(self.current_page > 0)
        self.next_page_btn.setEnabled(self.current_page < self.total_pages - 1)
        self.last_page_btn.setEnabled(self.current_page < self.total_pages - 1)

        # Show/hide jump to page functionality for large datasets
        if hasattr(self, 'jump_to_page_widget'):
            show_jump = self.violation_count > 10000 and self.total_pages > 20
            self.jump_to_page_widget.setVisible(show_jump)
            if show_jump:
                self.page_input.setPlaceholderText(f"1-{self.total_pages}")

    def go_to_first_page(self):
        """跳转到首页（彻底安全版本）"""
        try:
            enable_safe_messagebox()  # 防止分页过程中的弹窗
            if self.current_page != 0:
                old_page = self.current_page
                self.current_page = 0
                self._track_page_access(self.current_page)
                self.update_pagination_controls()
                self.refresh_current_page()
                self._schedule_memory_cleanup()
                # 恢复选中状态
                if hasattr(self, 'select_all_checkbox'):
                    self.update_select_all_checkbox_state()
                
                # Performance logging for large datasets
                if self.violation_count > 20000:
                    print(f"Navigated from page {old_page} to page {self.current_page} (first)")
        except Exception as e:
            print(f"跳转到首页失败: {e}")
        finally:
            disable_safe_messagebox()

    def go_to_prev_page(self):
        """跳转到上一页（彻底安全版本）"""
        try:
            enable_safe_messagebox()  # 防止分页过程中的弹窗
            if self.current_page > 0:
                old_page = self.current_page
                self.current_page -= 1
                self._track_page_access(self.current_page)
                self.update_pagination_controls()
                self.refresh_current_page()
                self._schedule_memory_cleanup()
                # 恢复选中状态
                if hasattr(self, 'select_all_checkbox'):
                    self.update_select_all_checkbox_state()
                
                # Performance logging for large datasets
                if self.violation_count > 20000:
                    print(f"Navigated from page {old_page} to page {self.current_page} (previous)")
        except Exception as e:
            print(f"跳转到上一页失败: {e}")
        finally:
            disable_safe_messagebox()

    def go_to_next_page(self):
        """跳转到下一页（彻底安全版本）"""
        try:
            enable_safe_messagebox()  # 防止分页过程中的弹窗
            if self.current_page < self.total_pages - 1:
                old_page = self.current_page
                self.current_page += 1
                self._track_page_access(self.current_page)
                self.update_pagination_controls()
                self.refresh_current_page()
                self._schedule_memory_cleanup()
                # 恢复选中状态
                if hasattr(self, 'select_all_checkbox'):
                    self.update_select_all_checkbox_state()
                
                # Performance logging for large datasets
                if self.violation_count > 20000:
                    print(f"Navigated from page {old_page} to page {self.current_page} (next)")
        except Exception as e:
            print(f"跳转到下一页失败: {e}")
        finally:
            disable_safe_messagebox()

    def go_to_last_page(self):
        """跳转到末页（彻底安全版本）"""
        try:
            enable_safe_messagebox()  # 防止分页过程中的弹窗
            if self.current_page != self.total_pages - 1:
                old_page = self.current_page
                self.current_page = max(0, self.total_pages - 1)
                self._track_page_access(self.current_page)
                self.update_pagination_controls()
                self.refresh_current_page()
                self._schedule_memory_cleanup()
                # 恢复选中状态
                if hasattr(self, 'select_all_checkbox'):
                    self.update_select_all_checkbox_state()
                
                # Performance logging for large datasets
                if self.violation_count > 20000:
                    print(f"Navigated from page {old_page} to page {self.current_page} (last)")
        except Exception as e:
            print(f"跳转到末页失败: {e}")
        finally:
            disable_safe_messagebox()

    def jump_to_page(self):
        """跳转到指定页码"""
        try:
            enable_safe_messagebox()
            
            page_text = self.page_input.text().strip()
            if not page_text:
                return
            
            try:
                target_page = int(page_text) - 1  # Convert to 0-based index
            except ValueError:
                print("Invalid page number")
                return
            
            if 0 <= target_page < self.total_pages:
                if target_page != self.current_page:
                    old_page = self.current_page
                    self.current_page = target_page
                    self._track_page_access(self.current_page)
                    self.update_pagination_controls()
                    self.refresh_current_page()
                    self._schedule_memory_cleanup()
                    # 恢复选中状态
                    if hasattr(self, 'select_all_checkbox'):
                        self.update_select_all_checkbox_state()
                    
                    # Clear input
                    self.page_input.clear()
                    
                    # Performance logging for large datasets
                    if self.violation_count > 20000:
                        print(f"Jumped from page {old_page} to page {self.current_page}")
            else:
                print(f"Page number out of range (1-{self.total_pages})")
                
        except Exception as e:
            print(f"跳转到指定页失败: {e}")
        finally:
            disable_safe_messagebox()

    def refresh_current_page(self):
        """刷新当前页显示（彻底避免弹窗版本）"""
        try:
            # 启用安全消息框模式，防止任何弹窗
            enable_safe_messagebox()

            import time
            start_time = time.time()

            # Try to use cached data for large datasets (>20K violations)
            if self.violation_count > 20000 and self._enhance_page_navigation_performance():
                load_time = time.time() - start_time
                print(f"页面刷新耗时 (缓存): {load_time:.3f}秒")
                return

            # 批量处理：先收集所有要移除的控件，然后一次性处理
            widgets_to_remove = []
            while self.content_layout.count():
                child = self.content_layout.takeAt(0)
                if child.widget():
                    widgets_to_remove.append(child.widget())

            # 将控件放入对象池复用，而不是直接销毁
            for widget in widgets_to_remove:
                try:
                    if len(self.widget_pool) < self.max_pool_size:
                        widget.setVisible(False)
                        widget.setParent(None)
                        self.widget_pool.append(widget)
                    else:
                        widget.deleteLater()
                except Exception as e:
                    print(f"处理控件回收失败: {e}")

            # 清除行控件列表
            self.row_widgets.clear()

            # 计算当前页的数据范围
            start_index = self.current_page * self.page_size
            end_index = min(start_index + self.page_size, self.model.rowCount())

            # 批量创建当前页的行（减少单次创建开销）
            new_widgets = []
            for row in range(start_index, end_index):
                try:
                    row_widget = self.create_row_widget_optimized(row)
                    if row_widget:
                        new_widgets.append(row_widget)
                        self.row_widgets.append(row_widget)
                except Exception as e:
                    # 静默处理单行创建错误，避免弹窗
                    print(f"创建行控件失败 (行 {row}): {e}")

            # 批量添加到布局
            for widget in new_widgets:
                try:
                    self.content_layout.addWidget(widget)
                except Exception as e:
                    print(f"添加控件到布局失败: {e}")

            # 添加弹性空间
            self.content_layout.addStretch()

            # 延迟更新几何信息，避免频繁重绘
            try:
                self.content_widget.adjustSize()
            except Exception as e:
                print(f"调整控件大小失败: {e}")

            # 性能统计
            load_time = time.time() - start_time
            if load_time > 0.1:  # 超过100ms记录性能警告
                print(f"页面刷新耗时: {load_time:.3f}秒 (页面大小: {len(new_widgets)})")

            # Start prefetching for next navigation after current page is loaded
            if self.violation_count > 5000:
                QTimer.singleShot(100, self._start_background_prefetching)

            # Performance monitoring - warn if page load exceeds 1 second (requirement 2.5)
            if load_time > 1.0:
                print(f"WARNING: Page load time {load_time:.3f}s exceeds 1 second requirement for {len(new_widgets)} violations")
                # Auto-adjust page size for better performance
                if self._auto_adjust_page_size_for_performance(load_time):
                    print("Page size auto-adjusted for better performance")
            elif load_time > 0.5:
                print(f"NOTICE: Page load time {load_time:.3f}s approaching 1 second limit")
            elif load_time < 0.3 and self.violation_count > 20000:
                # Try to optimize page size for good performance
                self._auto_adjust_page_size_for_performance(load_time)

        except Exception as e:
            # 静默处理整个刷新过程的错误
            print(f"刷新当前页失败: {e}")
            # 在状态栏显示错误而不是弹窗
            if hasattr(self, 'status_label'):
                self.status_label.setText(f"页面刷新失败: {str(e)[:30]}")
                QTimer.singleShot(3000, lambda: self.status_label.setText("就绪"))
        finally:
            # 确保在完成后恢复正常的消息框模式
            disable_safe_messagebox()

            # 恢复选中状态（如果有全选复选框）
            try:
                if hasattr(self, 'select_all_checkbox'):
                    self.update_select_all_checkbox_state()
            except Exception as e:
                print(f"恢复选中状态失败: {e}")

    def create_row_widget_optimized(self, row):
        """创建行控件（优化版本）"""
        # 尝试从对象池复用控件
        if self.widget_pool:
            row_widget = self.widget_pool.pop()
            row_widget.setVisible(True)
        else:
            row_widget = QWidget()

        return self.setup_row_widget(row_widget, row)

    def setup_row_widget(self, row_widget, row):
        """设置行控件内容（支持分组显示）"""
        try:
            # Use lazy loading for large datasets
            violation = self._get_lazy_loaded_violation_data(row)
            if not violation:
                return None

            # 检查是否为分组标识行
            is_group_header = violation.get('is_group_header', False)

            if is_group_header:
                return self._setup_group_header_widget(row_widget, violation)
            else:
                return self._setup_normal_row_widget(row_widget, row, violation)

        except Exception as e:
            # 静默处理整个行控件创建错误
            print(f"设置行控件失败 (行 {row}): {e}")
            return None

    def _setup_group_header_widget(self, row_widget, group_data):
        """设置分组标识行控件"""
        try:
            row_widget.setFixedHeight(40)  # 比普通行稍高

            # 设置分组标识行的背景样式
            row_widget.setStyleSheet("""
                QWidget {
                    background-color: #e6f0fa;
                    border: 1px solid #b3d9ff;
                    border-radius: 3px;
                    margin: 2px 0px;
                }
            """)

            # 清除现有布局
            if row_widget.layout():
                while row_widget.layout().count():
                    child = row_widget.layout().takeAt(0)
                    if child.widget():
                        child.widget().setParent(None)
            else:
                layout = QHBoxLayout(row_widget)
                layout.setContentsMargins(10, 5, 10, 5)
                layout.setSpacing(0)

            layout = row_widget.layout()

            # 创建分组标题标签
            group_title = group_data.get('group_title', '')
            violation_count = group_data.get('group_violation_count', 0)
            display_text = f"📁 {group_title} ({violation_count}条违例)"

            title_label = QLabel(display_text)
            title_label.setStyleSheet("""
                QLabel {
                    color: #003264;
                    font-weight: bold;
                    font-size: 14px;
                    background: transparent;
                    border: none;
                }
            """)

            layout.addWidget(title_label)
            layout.addStretch()  # 推到左侧

            return row_widget

        except Exception as e:
            print(f"设置分组标识行控件失败: {e}")
            return None

    def _setup_normal_row_widget(self, row_widget, row, violation):
        """设置普通违例行控件"""
        try:
            row_widget.setFixedHeight(self.row_height)

            # 计算相对于当前页的行索引（用于UI事件）
            relative_row = row - self.current_page * self.page_size

            # 设置行样式（基于绝对行索引）
            if row % 2 == 0:
                row_widget.setStyleSheet("background-color: white;")
            else:
                row_widget.setStyleSheet("background-color: #f9f9f9;")

            # 清除现有布局
            if row_widget.layout():
                while row_widget.layout().count():
                    child = row_widget.layout().takeAt(0)
                    if child.widget():
                        child.widget().setParent(None)
            else:
                layout = QHBoxLayout(row_widget)
                layout.setContentsMargins(5, 0, 5, 0)
                layout.setSpacing(0)

            layout = row_widget.layout()

            # 列宽配置 - 添加复选框列
            column_widths = [40, 60, 300, 100, 200, 80, 100, 80, 100]

            # 安全地创建列控件
            for col in range(min(len(column_widths), 9)):  # 9列包括复选框列
                try:
                    if col == 0:  # 复选框列
                        checkbox_widget = self.create_row_checkbox(violation)
                        if checkbox_widget:
                            checkbox_widget.setFixedWidth(column_widths[col])
                            layout.addWidget(checkbox_widget)
                    elif col == 8:  # 操作列（原来的第7列）
                        button = self.create_action_button(relative_row, violation)
                        if button:
                            button.setFixedWidth(column_widths[col])
                            layout.addWidget(button)
                    else:
                        # 其他列索引需要减1，因为添加了复选框列
                        label_col = col - 1
                        label = self.create_cell_label(relative_row, label_col, violation, column_widths[col])
                        if label:
                            layout.addWidget(label)
                        else:
                            print(f"警告: 第{col}列标签创建失败，行{relative_row}")
                except Exception as e:
                    # 静默处理单个列的错误，避免弹窗
                    print(f"创建列 {col} 控件失败: {e}")
                    # 添加占位标签
                    placeholder = QLabel("--")
                    placeholder.setFixedWidth(column_widths[col])
                    layout.addWidget(placeholder)

            return row_widget

        except Exception as e:
            # 静默处理整个行控件创建错误
            print(f"设置普通违例行控件失败 (行 {row}): {e}")
            return None

    def create_row_checkbox(self, violation):
        """创建行复选框"""
        try:
            # 跳过分组标识行
            if violation.get('is_group_header', False):
                placeholder = QWidget()
                return placeholder

            violation_id = violation.get('id')
            if not violation_id:
                placeholder = QWidget()
                return placeholder

            # 创建复选框容器
            checkbox_container = QWidget()
            checkbox_layout = QHBoxLayout(checkbox_container)
            checkbox_layout.setContentsMargins(0, 0, 0, 0)
            checkbox_layout.setAlignment(Qt.AlignCenter)

            # 创建复选框
            checkbox = QCheckBox()
            checkbox.setChecked(self.selected_violations.get(violation_id, False))

            # 存储violation_id到复选框属性中，便于后续查找
            checkbox.setProperty('violation_id', violation_id)

            # 连接信号，使用lambda避免闭包问题
            checkbox.stateChanged.connect(
                lambda state, vid=violation_id: self.on_row_checkbox_changed(vid, state)
            )

            checkbox_layout.addWidget(checkbox)

            # 存储violation_id到容器属性中，便于查找
            checkbox_container.setProperty('violation_id', violation_id)

            return checkbox_container

        except Exception as e:
            print(f"创建行复选框失败: {e}")
            placeholder = QWidget()
            return placeholder

    def create_row_widget(self, row):
        """创建行控件"""
        violation = self.model.get_violation_at_row(row)
        if not violation:
            return None

        row_widget = QWidget()
        row_widget.setFixedHeight(self.row_height)

        # 计算相对于当前页的行索引（用于UI事件）
        relative_row = row - self.current_page * self.page_size

        # 设置行样式（基于绝对行索引）
        if row % 2 == 0:
            row_widget.setStyleSheet("background-color: white;")
        else:
            row_widget.setStyleSheet("background-color: #f9f9f9;")

        layout = QHBoxLayout(row_widget)
        layout.setContentsMargins(5, 0, 5, 0)
        layout.setSpacing(0)

        # 列宽配置 - 添加复选框列
        column_widths = [40, 60, 300, 100, 200, 80, 100, 80, 100]

        for col in range(len(column_widths)):
            if col == 0:  # 复选框列
                checkbox_widget = self.create_row_checkbox(violation)
                checkbox_widget.setFixedWidth(column_widths[col])
                layout.addWidget(checkbox_widget)
            elif col == 8:  # 操作列（原来的第7列）
                button = self.create_action_button(relative_row, violation)
                button.setFixedWidth(column_widths[col])
                layout.addWidget(button)
            else:
                # 其他列索引需要减1，因为添加了复选框列
                label_col = col - 1
                label = self.create_cell_label(relative_row, label_col, violation, column_widths[col])
                layout.addWidget(label)

        return row_widget

    def create_cell_label(self, row, col, violation, width):
        """创建单元格标签（优化版本，避免错误弹窗）"""
        try:
            # 直接使用传入的violation对象获取数据，而不是通过model.index
            # 这样可以确保显示的是原始数据，而不是分页后的相对位置数据
            if col == 0:  # NUM
                # 使用全局连续编号：当前页 * 页面大小 + 行索引 + 1
                # 这样可以保证跨页面的连续编号，不会每页从1重新开始
                global_num = self.current_page * self.page_size + row + 1
                text = str(global_num)
            elif col == 1:  # 层级路径
                text = violation.get('hier', '')
            elif col == 2:  # 时间(ns)
                # 优先使用数据库中存储的格式化时间显示
                #text = violation.get('time_display', '')
                time_display = violation.get('time_display', '')
                if time_display:
                    if time_display.endswith('FS'):
                        time_ns = float(time_display[:-3]) / 1000000
                    elif time_display.endswith('PS'):
                        time_ns = float(time_display[:-3]) / 1000
                    else:
                        time_ns = float(time_display[:-3])
                    text = f"{time_ns:.3f}"
                else:
                    time_fs = violation.get('time_fs', 0)
                    time_ns = time_fs / 1000000 if time_fs else 0
                    text = f"{time_ns:.3f}"
            elif col == 3:  # 检查信息
                text = violation.get('check_info', '')
            elif col == 4:  # 状态
                status = violation.get('status', 'pending')
                # 直接使用状态映射，避免依赖主窗口方法
                status_map = {
                    'pending': '待确认',
                    'confirmed': '已确认',
                    'ignored': '已忽略'
                }
                text = status_map.get(status, status)
            elif col == 5:  # 确认人
                text = violation.get('confirmer', '')
            elif col == 6:  # 确认结果
                result = violation.get('result', '')
                # 直接使用结果映射，避免依赖主窗口方法
                result_map = {
                    'pass': '通过',
                    'issue': '有问题',
                    '': ''
                }
                text = result_map.get(result, result)
            else:
                # 回退到模型获取数据
                index = self.model.index(row, col)
                text = self.model.data(index, Qt.DisplayRole)

            label = QLabel(str(text) if text is not None else "")
            label.setFixedWidth(width)
            label.setStyleSheet("border-right: 1px solid #e0e0e0; padding: 5px;")

            # 设置对齐方式
            if col in [0, 2, 4, 5, 6]:  # NUM, 时间, 状态, 确认人, 确认结果
                label.setAlignment(Qt.AlignCenter)
            else:
                label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)

            # 直接根据violation数据设置颜色，而不是通过model.data
            try:
                # 设置前景色（基于确认状态）
                status = violation.get('status', 'pending')
                if status in ['confirmed', 'ignored']:
                    label.setStyleSheet(label.styleSheet() + "color: rgb(128, 128, 128);")  # 灰色
                else:
                    label.setStyleSheet(label.styleSheet() + "color: rgb(0, 0, 0);")  # 黑色
            except Exception as e:
                print(f"设置前景色失败: {e}")

            # 安全地设置状态列背景色
            if col == 4:
                try:
                    status = violation.get('status', 'pending')
                    if status == 'confirmed':
                        label.setStyleSheet(label.styleSheet() + "background-color: rgb(144, 238, 144);")  # 浅绿色
                    elif status == 'ignored':
                        label.setStyleSheet(label.styleSheet() + "background-color: rgb(255, 182, 193);")  # 浅红色
                    else:
                        label.setStyleSheet(label.styleSheet() + "background-color: rgb(255, 255, 224);")  # 浅黄色
                except Exception as e:
                    print(f"设置背景色失败: {e}")

            # 层级路径列支持双击复制
            if col == 1:
                def make_mouse_event_handler(r, c):
                    def mouse_event_handler(event):
                        try:
                            if event.type() == event.MouseButtonDblClick:
                                # 发射信号给主窗口处理
                                self.cell_double_clicked.emit(r, c)
                        except Exception as e:
                            print(f"双击事件处理失败: {e}")
                    return mouse_event_handler
                label.mouseDoubleClickEvent = make_mouse_event_handler(row, col)

            # 安全地设置工具提示
            try:
                # 为特定列设置工具提示
                if col == 1:  # 层级路径
                    tooltip = violation.get('hier', '')
                    if tooltip:
                        label.setToolTip(str(tooltip))
                elif col == 3:  # 检查信息
                    tooltip = violation.get('check_info', '')
                    if tooltip:
                        label.setToolTip(str(tooltip))
                else:
                    # 对于其他列，如果有index变量才使用model.data
                    if 'index' in locals():
                        tooltip = self.model.data(index, Qt.ToolTipRole)
                        if tooltip:
                            label.setToolTip(str(tooltip))
            except Exception as e:
                print(f"设置工具提示失败: {e}")

            return label

        except Exception as e:
            # 如果创建失败，返回一个简单的占位标签
            print(f"创建单元格标签失败 (行 {row}, 列 {col}): {e}")
            placeholder = QLabel("--")
            placeholder.setFixedWidth(width)
            placeholder.setStyleSheet("border-right: 1px solid #e0e0e0; padding: 5px;")
            return placeholder

    def create_action_button(self, row, violation):
        """创建操作按钮（彻底重写，避免信号问题导致的弹窗）"""
        try:
            # 启用安全消息框模式，防止按钮创建过程中的弹窗
            enable_safe_messagebox()

            # 检查是否为分组头，分组头不需要操作按钮
            if violation.get('is_group_header', False):
                placeholder_widget = QWidget()
                placeholder_widget.setStyleSheet("background: transparent;")
                return placeholder_widget

            status = violation.get('status', 'pending')
            violation_id = violation.get('id')

            # 验证数据有效性
            if not violation_id:
                print(f"警告: 违例记录缺少ID，行: {row}")
                placeholder_button = QPushButton("--")
                placeholder_button.setEnabled(False)
                return placeholder_button

            if status == 'pending':
                button = QPushButton("确认")
                button.setStyleSheet("""
                    QPushButton {
                        background-color: #4a9eff;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 4px 8px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #3d8ced;
                    }
                """)
            else:
                button = QPushButton("编辑")
                button.setStyleSheet("""
                    QPushButton {
                        color: #808080;
                        background-color: #f0f0f0;
                        border: 1px solid #c0c0c0;
                        border-radius: 4px;
                        padding: 4px 8px;
                    }
                    QPushButton:hover {
                        background-color: #e0e0e0;
                    }
                """)

            # 完全重写信号连接，避免lambda闭包问题
            # 将数据存储在按钮的属性中，而不是使用闭包
            button.setProperty('violation_id', violation_id)
            button.setProperty('action_type', status)
            button.setProperty('row_index', row)

            # 使用简单的槽函数，避免复杂的信号处理
            button.clicked.connect(self.handle_action_button_click)

            return button

        except Exception as e:
            # 如果按钮创建失败，返回一个简单的占位按钮
            print(f"创建操作按钮失败: {e}")
            placeholder_button = QPushButton("--")
            placeholder_button.setEnabled(False)
            return placeholder_button
        finally:
            # 确保恢复正常的消息框模式
            disable_safe_messagebox()

    def handle_action_button_click(self):
        """处理操作按钮点击（新的安全处理方法）"""
        try:
            # 获取发送信号的按钮
            button = self.sender()
            if not button:
                return

            # 从按钮属性中获取数据，避免闭包问题
            violation_id = button.property('violation_id')
            action_type = button.property('action_type')
            row_index = button.property('row_index')

            # 验证数据有效性
            if not violation_id:
                print(f"按钮缺少violation_id属性")
                return

            # 通过信号发送到主窗口处理，而不是直接调用方法
            self.action_button_clicked.emit(violation_id, action_type)

        except Exception as e:
            # 静默处理错误，绝对不显示弹窗
            print(f"处理按钮点击失败: {e}")

    def handle_standard_table_button_click(self):
        """处理标准表格中的按钮点击（安全版本）"""
        try:
            # 获取发送信号的按钮
            button = self.sender()
            if not button:
                return

            # 从按钮属性中获取数据
            violation_id = button.property('violation_id')
            action_type = button.property('action_type')

            # 验证数据有效性
            if not violation_id:
                print(f"标准表格按钮缺少violation_id属性")
                return

            # 通过信号发送到主窗口处理，而不是直接调用方法
            self.action_button_clicked.emit(violation_id, action_type)

        except Exception as e:
            # 静默处理错误，绝对不显示弹窗
            print(f"处理标准表格按钮点击失败: {e}")

    def _optimize_page_size_for_large_dataset(self):
        """Optimize page size based on violation complexity for large datasets (>20K violations)"""
        if not self.adaptive_page_sizing:
            return
            
        try:
            # Analyze violation complexity if not cached
            cache_key = f"{self.violation_count}_{hash(str(self.model.get_violation_at_row(0)) if self.model.rowCount() > 0 else '')}"
            if cache_key not in self.violation_complexity_cache:
                complexity_score = self._analyze_violation_complexity()
                self.violation_complexity_cache[cache_key] = complexity_score
            else:
                complexity_score = self.violation_complexity_cache[cache_key]
            
            # 优化页面大小计算算法 - 增加显示数量，提升用户体验
            if self.violation_count > 100000:
                # 超大数据集：适中页面大小，平衡性能和用户体验
                base_size = 80
                max_allowed = 120
            elif self.violation_count > 50000:
                # 大数据集：较大页面大小，提升用户体验
                base_size = 100
                max_allowed = 150
            elif self.violation_count > 20000:
                # 中等数据集：大页面大小
                base_size = 120
                max_allowed = 200
            elif self.violation_count > 5000:
                # 小数据集：更大页面大小
                base_size = 150
                max_allowed = 250
            else:
                # 很小数据集：使用基础页面大小
                base_size = self.base_page_size
                max_allowed = self.max_page_size
            
            # 根据复杂度调整页面大小（减少复杂度对页面大小的负面影响）
            complexity_factor = max(0.7, 1.0 - (complexity_score * 0.2))  # 减少复杂度影响
            adjusted_size = int(base_size * complexity_factor)
            
            # Consider system performance for very large datasets
            if self.violation_count > 50000:
                # Further reduce page size if system seems slow
                import psutil
                try:
                    memory_percent = psutil.virtual_memory().percent
                    cpu_percent = psutil.cpu_percent(interval=0.1)
                    
                    if memory_percent > 85 or cpu_percent > 85:
                        # 系统压力很大时，适度减少页面大小
                        adjusted_size = int(adjusted_size * 0.8)  # 减少幅度从0.7调整到0.8
                        print(f"System under pressure (RAM: {memory_percent}%, CPU: {cpu_percent}%), reducing page size")
                except ImportError:
                    # psutil不可用时，使用保守方法，但减少幅度更小
                    adjusted_size = int(adjusted_size * 0.9)  # 从0.8调整到0.9
            
            # 根据屏幕高度进一步优化页面大小
            try:
                from PyQt5.QtWidgets import QApplication
                screen = QApplication.primaryScreen()
                if screen:
                    screen_height = screen.availableGeometry().height()
                    # 根据屏幕高度计算可显示的行数
                    available_height = screen_height - 200  # 减去窗口标题栏、工具栏等
                    max_visible_rows = max(30, int(available_height / self.row_height))

                    # 页面大小不应超过屏幕可显示的行数的1.5倍
                    screen_based_max = int(max_visible_rows * 1.5)
                    adjusted_size = min(adjusted_size, screen_based_max)

                    print(f"Screen-based optimization: max_visible_rows={max_visible_rows}, screen_based_max={screen_based_max}")
            except Exception as e:
                print(f"Screen-based optimization failed: {e}")

            # Ensure within bounds
            self.page_size = max(self.min_page_size, min(max_allowed, adjusted_size))

            # Clear prefetch cache when page size changes
            if hasattr(self, 'prefetch_cache'):
                self.prefetch_cache.clear()

            print(f"Optimized page size for {self.violation_count} violations: {self.page_size} (complexity: {complexity_score:.2f})")
            
        except Exception as e:
            print(f"Failed to optimize page size: {e}")
            self.page_size = self.base_page_size

    def _analyze_violation_complexity(self) -> float:
        """Analyze violation data complexity to determine optimal page sizing"""
        try:
            if self.violation_count == 0:
                return 0.0
            
            # Sample a subset of violations for analysis
            sample_size = min(100, self.violation_count)
            complexity_factors = []
            
            for i in range(0, min(sample_size, self.model.rowCount())):
                violation = self.model.get_violation_at_row(i)
                if not violation:
                    continue
                
                # Calculate complexity based on various factors
                complexity = 0.0
                
                # Path length complexity
                hier_path = violation.get('hier', '')
                if hier_path:
                    complexity += min(len(hier_path) / 100.0, 1.0) * 0.3
                
                # Check info complexity
                check_info = violation.get('check_info', '')
                if check_info:
                    complexity += min(len(check_info) / 200.0, 1.0) * 0.2
                
                # Time value complexity (higher precision = higher complexity)
                time_fs = violation.get('time_fs', 0)
                if time_fs:
                    time_str = str(time_fs)
                    complexity += min(len(time_str) / 20.0, 1.0) * 0.1
                
                # Status complexity (pending violations are more complex to render)
                status = violation.get('status', 'pending')
                if status == 'pending':
                    complexity += 0.4
                
                complexity_factors.append(min(complexity, 1.0))
            
            # Return average complexity
            return sum(complexity_factors) / len(complexity_factors) if complexity_factors else 0.0
            
        except Exception as e:
            print(f"Failed to analyze violation complexity: {e}")
            return 0.5  # Default moderate complexity

    def _start_background_prefetching(self):
        """Start background prefetching for improved navigation performance"""
        try:
            # Clear existing cache
            self.prefetch_cache.clear()
            
            # Start prefetching timer (prefetch next/previous pages)
            if not self.prefetch_timer.isActive():
                self.prefetch_timer.start(500)  # Start after 500ms delay
                
        except Exception as e:
            print(f"Failed to start background prefetching: {e}")

    def _background_prefetch(self):
        """Background prefetch next and previous pages for smooth navigation"""
        try:
            if self.violation_count <= 1000:  # Only prefetch for larger datasets
                return
            
            pages_to_prefetch = []
            
            # Prefetch next page
            if self.current_page < self.total_pages - 1:
                pages_to_prefetch.append(self.current_page + 1)
            
            # Prefetch previous page
            if self.current_page > 0:
                pages_to_prefetch.append(self.current_page - 1)
            
            # Prefetch up to 2 pages ahead for very large datasets
            if self.violation_count > 50000:
                if self.current_page < self.total_pages - 2:
                    pages_to_prefetch.append(self.current_page + 2)
            
            # Perform prefetching
            for page_num in pages_to_prefetch:
                if page_num not in self.prefetch_cache:
                    self._prefetch_page_data(page_num)
            
            # Clean up old cache entries to prevent memory bloat
            self._cleanup_prefetch_cache()
            
        except Exception as e:
            print(f"Background prefetch failed: {e}")

    def _prefetch_page_data(self, page_num: int):
        """Prefetch data for a specific page"""
        try:
            if page_num < 0 or page_num >= self.total_pages:
                return
            
            start_index = page_num * self.page_size
            end_index = min(start_index + self.page_size, self.model.rowCount())
            
            # Create lightweight data structure for prefetched page
            page_data = {
                'page_num': page_num,
                'violations': [],
                'timestamp': time.time()
            }
            
            # Prefetch violation data for this page
            for row in range(start_index, end_index):
                violation = self.model.get_violation_at_row(row)
                if violation:
                    # Store only essential data to minimize memory usage
                    essential_data = {
                        'id': violation.get('id'),
                        'hier': violation.get('hier', ''),
                        'time_fs': violation.get('time_fs', 0),
                        'check_info': violation.get('check_info', ''),
                        'status': violation.get('status', 'pending'),
                        'confirmed_by': violation.get('confirmed_by', ''),
                        'confirm_result': violation.get('confirm_result', '')
                    }
                    page_data['violations'].append(essential_data)
            
            # Store in cache with size limit
            if len(self.prefetch_cache) < 10:  # Limit cache size
                self.prefetch_cache[page_num] = page_data
                print(f"Prefetched page {page_num} with {len(page_data['violations'])} violations")
            
        except Exception as e:
            print(f"Failed to prefetch page {page_num}: {e}")

    def _cleanup_prefetch_cache(self):
        """Clean up old prefetch cache entries to prevent memory bloat"""
        try:
            current_time = time.time()
            cache_timeout = 1800  # 30 minutes
            
            # Remove entries older than timeout
            expired_pages = []
            for page_num, data in self.prefetch_cache.items():
                if current_time - data['timestamp'] > cache_timeout:
                    expired_pages.append(page_num)
            
            for page_num in expired_pages:
                del self.prefetch_cache[page_num]
            
            # Keep only nearby pages if cache is too large
            if len(self.prefetch_cache) > 5:
                # Keep only pages within 2 of current page
                pages_to_keep = set()
                for offset in range(-2, 3):
                    nearby_page = self.current_page + offset
                    if 0 <= nearby_page < self.total_pages:
                        pages_to_keep.add(nearby_page)
                
                pages_to_remove = []
                for page_num in self.prefetch_cache:
                    if page_num not in pages_to_keep:
                        pages_to_remove.append(page_num)
                
                for page_num in pages_to_remove:
                    del self.prefetch_cache[page_num]
            
        except Exception as e:
            print(f"Failed to cleanup prefetch cache: {e}")

    def _get_cached_page_data(self, page_num: int):
        """Get cached page data if available"""
        try:
            if page_num in self.prefetch_cache:
                cache_data = self.prefetch_cache[page_num]
                # Update timestamp to mark as recently used
                cache_data['timestamp'] = time.time()
                return cache_data['violations']
            return None
        except Exception as e:
            print(f"Failed to get cached page data: {e}")
            return None

    def _enhance_page_navigation_performance(self):
        """Enhance page navigation performance for large datasets"""
        try:
            # Use cached data if available for current page
            cached_data = self._get_cached_page_data(self.current_page)
            if cached_data and self.violation_count > 20000:
                print(f"Using cached data for page {self.current_page}")
                return self._render_page_from_cache(cached_data)
            
            # Fall back to normal rendering
            return False
            
        except Exception as e:
            print(f"Failed to enhance page navigation: {e}")
            return False

    def _render_page_from_cache(self, cached_violations):
        """Render page using cached violation data"""
        try:
            # Clear current content
            while self.content_layout.count():
                child = self.content_layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
            
            self.row_widgets.clear()
            
            # Create rows from cached data
            for i, violation in enumerate(cached_violations):
                try:
                    row_widget = self._create_row_from_cached_data(i, violation)
                    if row_widget:
                        self.content_layout.addWidget(row_widget)
                        self.row_widgets.append(row_widget)
                except Exception as e:
                    print(f"Failed to create row from cache: {e}")
            
            # Add stretch
            self.content_layout.addStretch()
            self.content_widget.adjustSize()
            
            return True
            
        except Exception as e:
            print(f"Failed to render page from cache: {e}")
            return False

    def _create_row_from_cached_data(self, row_index: int, violation_data: dict):
        """Create row widget from cached violation data"""
        try:
            row_widget = QWidget()
            row_widget.setFixedHeight(self.row_height)
            
            # Set row style
            if row_index % 2 == 0:
                row_widget.setStyleSheet("background-color: white;")
            else:
                row_widget.setStyleSheet("background-color: #f9f9f9;")
            
            layout = QHBoxLayout(row_widget)
            layout.setContentsMargins(5, 0, 5, 0)
            layout.setSpacing(0)
            
            # Column widths - 添加复选框列
            column_widths = [40, 60, 300, 100, 200, 80, 100, 80, 100]

            # Create columns
            # 计算全局连续编号
            global_num = self.current_page * self.page_size + row_index + 1
            columns_data = [
                None,  # 复选框列
                str(global_num),  # NUM - 使用全局连续编号
                violation_data.get('hier', ''),  # 层级路径
                self._format_time_display(violation_data.get('time_fs', 0)),  # 时间
                violation_data.get('check_info', ''),  # 检查信息
                self.get_status_display(violation_data.get('status', 'pending')),  # 状态 - 使用显示转换
                violation_data.get('confirmed_by', ''),  # 确认人
                self.get_result_display(violation_data.get('confirm_result', '')),  # 确认结果 - 使用显示转换
                None  # 操作列
            ]

            for col, (data, width) in enumerate(zip(columns_data, column_widths)):
                if col == 0:  # 复选框列
                    checkbox_widget = self._create_cached_checkbox(violation_data)
                    checkbox_widget.setFixedWidth(width)
                    layout.addWidget(checkbox_widget)
                elif col == 8:  # 操作列
                    button = self._create_cached_action_button(row_index, violation_data)
                    button.setFixedWidth(width)
                    layout.addWidget(button)
                else:
                    label = QLabel(str(data) if data is not None else "")
                    label.setFixedWidth(width)
                    label.setStyleSheet("border-right: 1px solid #e0e0e0; padding: 5px;")

                    # Set alignment
                    if col in [1, 3, 5, 6, 7]:  # 调整列索引
                        label.setAlignment(Qt.AlignCenter)
                    else:
                        label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)

                    # Set status column color
                    if col == 5:  # 状态列（调整列索引）
                        status = violation_data.get('status', 'pending')
                        if status == 'confirmed':
                            label.setStyleSheet(label.styleSheet() + "background-color: #d4edda; color: #155724;")
                        elif status == 'false_violation':
                            label.setStyleSheet(label.styleSheet() + "background-color: #f8d7da; color: #721c24;")

                    layout.addWidget(label)
            
            return row_widget
            
        except Exception as e:
            print(f"Failed to create row from cached data: {e}")
            return None

    def _create_cached_action_button(self, row_index: int, violation_data: dict):
        """Create action button from cached violation data"""
        try:
            status = violation_data.get('status', 'pending')
            violation_id = violation_data.get('id')
            
            if status == 'pending':
                button = QPushButton("确认")
                button.setStyleSheet("""
                    QPushButton {
                        background-color: #4a9eff;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 4px 8px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #3d8ced;
                    }
                """)
            else:
                button = QPushButton("编辑")
                button.setStyleSheet("""
                    QPushButton {
                        color: #808080;
                        background-color: #f0f0f0;
                        border: 1px solid #c0c0c0;
                        border-radius: 4px;
                        padding: 4px 8px;
                    }
                    QPushButton:hover {
                        background-color: #e0e0e0;
                    }
                """)
            
            # Set button properties
            button.setProperty('violation_id', violation_id)
            button.setProperty('action_type', status)
            button.setProperty('row_index', row_index)
            button.clicked.connect(self.handle_action_button_click)
            
            return button
            
        except Exception as e:
            print(f"Failed to create cached action button: {e}")
            placeholder = QPushButton("--")
            placeholder.setEnabled(False)
            return placeholder

    def _create_cached_checkbox(self, violation_data):
        """创建缓存的复选框"""
        try:
            violation_id = violation_data.get('id')
            if not violation_id:
                placeholder = QWidget()
                return placeholder

            # 创建复选框容器
            checkbox_container = QWidget()
            checkbox_layout = QHBoxLayout(checkbox_container)
            checkbox_layout.setContentsMargins(0, 0, 0, 0)
            checkbox_layout.setAlignment(Qt.AlignCenter)

            # 创建复选框
            checkbox = QCheckBox()
            checkbox.setChecked(self.selected_violations.get(violation_id, False))

            # 存储violation_id到复选框属性中
            checkbox.setProperty('violation_id', violation_id)

            # 连接信号
            checkbox.stateChanged.connect(
                lambda state, vid=violation_id: self.on_row_checkbox_changed(vid, state)
            )

            checkbox_layout.addWidget(checkbox)

            # 存储violation_id到容器属性中
            checkbox_container.setProperty('violation_id', violation_id)

            return checkbox_container

        except Exception as e:
            print(f"创建缓存复选框失败: {e}")
            placeholder = QWidget()
            return placeholder

    def _format_time_display(self, time_fs: int) -> str:
        """Format time for display"""
        try:
            if time_fs == 0:
                return "0"
            
            # Convert femtoseconds to nanoseconds
            time_ns = time_fs / 1000000
            
            if time_ns >= 1000:
                return f"{time_ns/1000:.2f}us"
            else:
                return f"{time_ns:.2f}ns"
                
        except Exception as e:
            return str(time_fs)

    def _cleanup_memory(self):
        """Clean up memory by releasing unused page data and widgets"""
        try:
            import time
            current_time = time.time()
            
            # Clean up widget pool if it's too large
            if len(self.widget_pool) > self.max_pool_size:
                excess_widgets = len(self.widget_pool) - self.max_pool_size
                for _ in range(excess_widgets):
                    if self.widget_pool:
                        widget = self.widget_pool.pop()
                        widget.deleteLater()
            
            # Clean up old page access records
            old_access_times = []
            for page_num, access_time in self.last_page_access_time.items():
                if current_time - access_time > 600:  # 10 minutes
                    old_access_times.append(page_num)
            
            for page_num in old_access_times:
                del self.last_page_access_time[page_num]
            
            # Force garbage collection for large datasets
            if self.violation_count > 50000:
                import gc
                gc.collect()
                print("Memory cleanup completed for large dataset")
            
        except Exception as e:
            print(f"Memory cleanup failed: {e}")

    def _schedule_memory_cleanup(self):
        """Schedule memory cleanup after page navigation"""
        if self.violation_count > 20000:
            # Schedule cleanup after 5 seconds of inactivity
            self.memory_cleanup_timer.start(5000)

    def _track_page_access(self, page_num: int):
        """Track page access time for memory management"""
        import time
        self.last_page_access_time[page_num] = time.time()

    def _get_lazy_loaded_violation_data(self, row: int):
        """Get violation data with lazy loading for memory efficiency"""
        try:
            if not self.lazy_loading_enabled or self.violation_count <= 5000:
                return self.model.get_violation_at_row(row)
            
            # For large datasets, load only essential data initially
            violation = self.model.get_violation_at_row(row)
            if not violation:
                return None
            
            # Return lightweight version for initial display
            essential_data = {
                'id': violation.get('id'),
                'hier': violation.get('hier', '')[:100] + '...' if len(violation.get('hier', '')) > 100 else violation.get('hier', ''),
                'time_fs': violation.get('time_fs', 0),
                'check_info': violation.get('check_info', '')[:50] + '...' if len(violation.get('check_info', '')) > 50 else violation.get('check_info', ''),
                'status': violation.get('status', 'pending'),
                'confirmed_by': violation.get('confirmed_by', ''),
                'confirm_result': violation.get('confirm_result', ''),
                # 保留分组头标识，这对于正确渲染非常重要
                'is_group_header': violation.get('is_group_header', False),
                'group_title': violation.get('group_title', ''),
                'group_violation_count': violation.get('group_violation_count', 0),
                '_lazy_loaded': True
            }
            
            return essential_data
            
        except Exception as e:
            print(f"Lazy loading failed for row {row}: {e}")
            return self.model.get_violation_at_row(row)

    def _auto_adjust_page_size_for_performance(self, load_time: float):
        """Automatically adjust page size if performance is poor"""
        try:
            if load_time > 1.0 and self.page_size > self.min_page_size:
                # Performance is poor, reduce page size
                old_page_size = self.page_size
                reduction_factor = min(0.7, 1.0 / load_time)  # Reduce more for worse performance
                new_page_size = max(self.min_page_size, int(self.page_size * reduction_factor))
                
                if new_page_size != old_page_size:
                    self.page_size = new_page_size
                    print(f"Auto-adjusted page size from {old_page_size} to {new_page_size} due to poor performance ({load_time:.3f}s)")
                    
                    # Recalculate pagination
                    self.calculate_pagination()
                    self.update_pagination_controls()
                    
                    # Clear caches as page structure changed
                    self.prefetch_cache.clear()
                    
                    return True
            elif load_time < 0.3 and self.page_size < self.max_page_size and self.violation_count > 20000:
                # Performance is good, we can try increasing page size slightly
                old_page_size = self.page_size
                new_page_size = min(self.max_page_size, int(self.page_size * 1.2))
                
                if new_page_size != old_page_size:
                    self.page_size = new_page_size
                    print(f"Auto-increased page size from {old_page_size} to {new_page_size} due to good performance ({load_time:.3f}s)")
                    
                    # Recalculate pagination
                    self.calculate_pagination()
                    self.update_pagination_controls()
                    
                    return True
            
            return False
            
        except Exception as e:
            print(f"Failed to auto-adjust page size: {e}")
            return False

    def get_selected_rows(self):
        """获取当前页面中选中的行索引列表（支持批量确认功能）"""
        try:
            selected_rows = []

            # 获取当前页面的数据范围
            start_index = self.current_page * self.page_size
            end_index = min(start_index + self.page_size, self.model.rowCount())

            # 检查当前页面中每一行的选中状态
            for row in range(start_index, end_index):
                violation = self.model.get_violation_at_row(row)
                if violation and not violation.get('is_group_header', False):
                    violation_id = violation.get('id')
                    if violation_id and self.selected_violations.get(violation_id, False):
                        # 返回相对于当前页面的行索引
                        relative_row = row - start_index
                        selected_rows.append(row)  # 返回绝对行索引，与标准表格模式一致

            print(f"高性能表格获取选中行: {len(selected_rows)} 行")
            return selected_rows

        except Exception as e:
            print(f"获取选中行失败: {e}")
            return []

    def clear_selection(self):
        """清空所有选中状态"""
        try:
            self.selected_violations.clear()
            self.select_all_state = False
            if hasattr(self, 'select_all_checkbox'):
                self.select_all_checkbox.setCheckState(Qt.Unchecked)
            self.refresh_current_page()
        except Exception as e:
            print(f"清空选中状态失败: {e}")

    def get_selected_violation_count(self):
        """获取选中的违例总数（跨所有页面）"""
        return sum(1 for selected in self.selected_violations.values() if selected)

    def refresh_current_page_with_selection(self):
        """刷新当前页显示并恢复选中状态"""
        try:
            # 调用原有的刷新逻辑
            self.refresh_current_page()

            # 刷新后更新全选复选框状态
            self.update_select_all_checkbox_state()

        except Exception as e:
            print(f"刷新当前页失败: {e}")


class TimingViolationWindow(NonModalDialog):
    """时序违例确认主窗口"""
    
    def __init__(self, parent=None):
        super().__init__(parent, "后仿时序违例确认工具")
        self.setWindowTitle("后仿时序违例确认工具")
        self.resize(1400, 900)
        self.setMinimumSize(1200, 700)
        
        # 数据模型
        self.data_model = ViolationDataModel()
        self.parser = VioLogParser()
        self.async_parser = None

        # 当前数据
        self.current_violations = []
        self.current_case_name = ""
        self.current_corner = ""
        self.current_file_path = ""
        
        # 搜索过滤相关
        self.original_violations = []  # 保存原始违例数据
        self.filtered_violations = []  # 保存过滤后的违例数据
        self.search_filter_text = ""   # 当前搜索过滤文本

        # 性能优化组件 (Legacy)
        self.performance_optimizer = PerformanceOptimizer()
        self.strategy_manager = IntelligentStrategyManager(self.performance_optimizer)
        
        # 性能优化标志
        self.use_high_performance_table = False
        self.performance_threshold = 1000  # 提高阈值到1000行，超过1000行就使用高性能表格
        self.current_strategy = None  # 当前使用的策略

        # 性能监控
        self.performance_stats = {
            'last_load_time': 0,
            'last_record_count': 0,
            'memory_usage_mb': 0
        }
        
        # Performance Integration System (New)
        self.integration_adapter = None
        self.compatibility_manager = None
        self.performance_integration_enabled = False
        
        # Initialize performance integration
        self._initialize_performance_integration()

        # 数据库状态标志
        self._db_busy_with_background_save = False

        # 初始化UI
        self.init_ui()
        self.apply_runsim_theme()
        
        # 连接信号
        self.connect_signals()
        
        # Apply performance integration patches after UI is initialized
        self._apply_performance_integration_patches()
    
    def on_search_text_changed(self, text: str):
        """搜索文本变更处理"""
        self.search_filter_text = text.strip()
        
        # 如果搜索文本为空，显示所有违例
        if not self.search_filter_text:
            self.clear_search_filter()
            return
        
        # 根据当前表格模式决定是否实时搜索
        if self._is_using_high_performance_table():
            # 高性能表格模式：不实时搜索，等待用户点击搜索按钮
            # 更新搜索统计但不刷新表格
            self._update_search_stats_only()
        else:
            # 标准表格模式：实时搜索
            self.apply_search_filter()
    
    def on_search_button_clicked(self):
        """搜索按钮点击处理"""
        if self.search_filter_text:
            self.apply_search_filter()
        else:
            # 如果搜索框为空，清除搜索
            self.clear_search_filter()
    
    def apply_search_filter(self):
        """应用搜索过滤"""
        if not self.original_violations:
            return
        
        search_text = self.search_filter_text.lower()
        
        # 过滤违例数据
        filtered_violations = []
        for violation in self.original_violations:
            # 跳过分组标识行
            if violation.get('is_group_header', False):
                continue
                
            # 获取层级路径进行匹配
            hier_path = violation.get('hier', '').lower()
            if search_text in hier_path:
                filtered_violations.append(violation)
        
        # 更新过滤后的数据
        self.filtered_violations = filtered_violations
        self.current_violations = filtered_violations
        
        # 更新搜索统计
        total_count = len([v for v in self.original_violations if not v.get('is_group_header', False)])
        filtered_count = len(filtered_violations)
        self.search_stats_label.setText(f"搜索结果: {filtered_count}/{total_count} 条")
        
        # 刷新表格显示
        self._refresh_table_display()
        
        print(f"搜索过滤: '{self.search_filter_text}' -> {filtered_count}/{total_count} 条结果")
    
    def clear_search_filter(self):
        """清除搜索过滤"""
        self.search_filter_text = ""
        self.search_edit.clear()
        self.search_stats_label.setText("")
        
        # 恢复原始数据
        if self.original_violations:
            self.current_violations = self.original_violations.copy()
            self._refresh_table_display()
            
            # 更新统计
            total_count = len([v for v in self.original_violations if not v.get('is_group_header', False)])
            print(f"清除搜索过滤，恢复显示 {total_count} 条违例")
    
    def _refresh_table_display(self):
        """刷新表格显示（不重新加载数据）"""
        try:
            # 根据当前违例数量选择合适的表格类型
            if len(self.current_violations) > self.performance_threshold:
                self._use_high_performance_table(self.current_violations)
            else:
                self._use_standard_table(self.current_violations)
            
            # 更新搜索按钮的可见性
            self._update_search_button_visibility()
            
            # 更新进度显示
            self.update_progress_display()
            
        except Exception as e:
            print(f"刷新表格显示失败: {e}")
    
    def _is_using_high_performance_table(self) -> bool:
        """检测当前是否使用高性能表格"""
        return (hasattr(self, 'high_performance_table') and 
                self.high_performance_table.isVisible())
    
    def _update_search_button_visibility(self):
        """更新搜索按钮的可见性"""
        if hasattr(self, 'search_btn'):
            is_high_performance = self._is_using_high_performance_table()
            self.search_btn.setVisible(is_high_performance)
            
            # 更新搜索框的提示文本
            if is_high_performance:
                self.search_edit.setPlaceholderText("输入层级路径关键字，点击搜索按钮或按回车键...")
                self.search_edit.setToolTip("输入层级路径中的关键字，点击搜索按钮或按回车键进行过滤")
            else:
                self.search_edit.setPlaceholderText("输入层级路径关键字进行过滤...")
                self.search_edit.setToolTip("输入层级路径中的关键字，支持实时模糊匹配")
    
    def _update_search_stats_only(self):
        """仅更新搜索统计信息，不刷新表格"""
        if not self.original_violations or not self.search_filter_text:
            self.search_stats_label.setText("")
            return
        
        search_text = self.search_filter_text.lower()
        
        # 计算匹配的违例数量
        filtered_count = 0
        for violation in self.original_violations:
            # 跳过分组标识行
            if violation.get('is_group_header', False):
                continue
                
            # 获取层级路径进行匹配
            hier_path = violation.get('hier', '').lower()
            if search_text in hier_path:
                filtered_count += 1
        
        # 更新搜索统计
        total_count = len([v for v in self.original_violations if not v.get('is_group_header', False)])
        self.search_stats_label.setText(f"预计搜索结果: {filtered_count}/{total_count} 条 (点击搜索按钮查看)")
        
        print(f"搜索预览: '{self.search_filter_text}' -> {filtered_count}/{total_count} 条结果")
    
    def _backup_original_violations(self):
        """备份原始违例数据"""
        if self.current_violations and not self.original_violations:
            self.original_violations = self.current_violations.copy()
            print(f"备份原始违例数据: {len(self.original_violations)} 条")
    
    def init_ui(self):
        """初始化用户界面"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(5)
        main_layout.setContentsMargins(5, 5, 5, 5)

        # 创建工具栏
        toolbar = self.create_toolbar()
        main_layout.addWidget(toolbar)

        # 创建控制面板
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)

        # 创建违例列表
        violation_table = self.create_violation_table()
        main_layout.addWidget(violation_table)

        # 创建状态栏
        self.status_bar = self.create_status_bar()
        main_layout.addWidget(self.status_bar)

        # 设置布局比例
        main_layout.setStretchFactor(toolbar, 0)
        main_layout.setStretchFactor(control_panel, 0)
        main_layout.setStretchFactor(violation_table, 1)
        main_layout.setStretchFactor(self.status_bar, 0)
    
    def create_toolbar(self):
        """创建工具栏"""
        # 创建工具栏容器
        toolbar_widget = QWidget()
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(5, 5, 5, 5)
        
        # 选择文件按钮
        self.select_file_btn_toolbar = QPushButton("选择文件")
        self.select_file_btn_toolbar.setToolTip("选择vio_summary.log文件")
        self.select_file_btn_toolbar.clicked.connect(self.select_log_file)
        toolbar_layout.addWidget(self.select_file_btn_toolbar)

        # 回归批量扫描按钮
        self.regression_batch_btn = QPushButton("回归批量扫描")
        self.regression_batch_btn.setToolTip("扫描回归目录中的多个vio_summary.log文件")
        self.regression_batch_btn.clicked.connect(self.show_regression_batch_dialog)
        toolbar_layout.addWidget(self.regression_batch_btn)

        # 分隔符
        toolbar_layout.addWidget(QLabel("|"))

        # 刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.setToolTip("刷新违例列表")
        self.refresh_btn.clicked.connect(self.refresh_data)
        toolbar_layout.addWidget(self.refresh_btn)

        # 分隔符
        toolbar_layout.addWidget(QLabel("|"))

        # 导出按钮
        self.export_excel_btn = QPushButton("导出Excel")
        self.export_excel_btn.setToolTip("导出为Excel文件")
        self.export_excel_btn.clicked.connect(self.export_to_excel)
        toolbar_layout.addWidget(self.export_excel_btn)

        self.export_csv_btn = QPushButton("导出CSV")
        self.export_csv_btn.setToolTip("导出为CSV文件")
        self.export_csv_btn.clicked.connect(self.export_to_csv)
        toolbar_layout.addWidget(self.export_csv_btn)

        # 分隔符
        toolbar_layout.addWidget(QLabel("|"))

        # 清除历史按钮
        self.clear_btn = QPushButton("清除历史")
        self.clear_btn.setToolTip("清除当前用例的历史数据")
        self.clear_btn.clicked.connect(self.clear_history)
        toolbar_layout.addWidget(self.clear_btn)

        # 历史管理按钮
        self.history_mgmt_btn = QPushButton("历史管理")
        self.history_mgmt_btn.setToolTip("管理历史确认模式")
        self.history_mgmt_btn.clicked.connect(self.show_history_management)
        toolbar_layout.addWidget(self.history_mgmt_btn)

        # 分隔符
        toolbar_layout.addWidget(QLabel("|"))

        # 数据库合并按钮
        self.merge_db_btn = QPushButton("数据库合并")
        self.merge_db_btn.setToolTip("合并其他用户的时序违例数据库")
        self.merge_db_btn.clicked.connect(self.show_database_merge)
        toolbar_layout.addWidget(self.merge_db_btn)

        # 分隔符
        toolbar_layout.addWidget(QLabel("|"))

        # 网页显示按钮
        self.web_display_btn = QPushButton("网页显示")
        self.web_display_btn.setToolTip("启动实时网页展示服务")
        self.web_display_btn.clicked.connect(self.handle_web_display_click)
        toolbar_layout.addWidget(self.web_display_btn)

        # 添加弹性空间
        toolbar_layout.addStretch()

        return toolbar_widget
    
    def create_control_panel(self) -> QGroupBox:
        """创建控制面板"""
        group_box = QGroupBox("控制面板")
        layout = QVBoxLayout(group_box)
        
        # 第一行：文件和基本信息
        first_row = QHBoxLayout()
        
        # 文件路径
        first_row.addWidget(QLabel("文件路径:"))
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setReadOnly(True)
        self.file_path_edit.setPlaceholderText("请选择vio_summary.log文件")
        first_row.addWidget(self.file_path_edit)
        
        self.select_file_btn = QPushButton("选择文件...")
        self.select_file_btn.clicked.connect(self.select_log_file)
        first_row.addWidget(self.select_file_btn)
        
        layout.addLayout(first_row)
        
        # 第二行：用例信息和复位时间
        second_row = QHBoxLayout()
        
        # 用例名称
        second_row.addWidget(QLabel("用例名称:"))
        self.case_name_edit = QLineEdit()
        self.case_name_edit.setPlaceholderText("自动检测")
        second_row.addWidget(self.case_name_edit)
        
        # Corner选择
        second_row.addWidget(QLabel("Corner:"))
        self.corner_combo = QComboBox()
        self.corner_combo.addItem("请选择...")
        self.corner_combo.addItems(CaseInfoParser.get_valid_corners())
        second_row.addWidget(self.corner_combo)
        
        # 复位时间
        second_row.addWidget(QLabel("复位时间(ns):"))
        self.reset_time_edit = QLineEdit()
        self.reset_time_edit.setPlaceholderText("1000")
        self.reset_time_edit.setText("1000")
        second_row.addWidget(self.reset_time_edit)

        # 复位区间
        second_row.addWidget(QLabel("复位区间(ns):"))
        self.reset_interval_edit = QLineEdit()
        self.reset_interval_edit.setPlaceholderText("5000~6000")
        self.reset_interval_edit.setToolTip("输入时间区间，格式：开始时间~结束时间，例如：5000~6000")
        second_row.addWidget(self.reset_interval_edit)

        layout.addLayout(second_row)
        
        # 第三行：搜索过滤功能
        third_row = QHBoxLayout()
        
        # 搜索功能
        third_row.addWidget(QLabel("搜索层级路径:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入层级路径关键字进行过滤...")
        self.search_edit.setToolTip("输入层级路径中的关键字，支持模糊匹配")
        self.search_edit.textChanged.connect(self.on_search_text_changed)
        # 支持回车键触发搜索
        self.search_edit.returnPressed.connect(self.on_search_button_clicked)
        third_row.addWidget(self.search_edit)
        
        # 搜索按钮（在高性能表格模式下显示）
        self.search_btn = QPushButton("搜索")
        self.search_btn.setToolTip("点击搜索（高性能表格模式）或按回车键")
        self.search_btn.clicked.connect(self.on_search_button_clicked)
        self.search_btn.setVisible(False)  # 默认隐藏，在高性能模式下显示
        third_row.addWidget(self.search_btn)
        
        # 清除搜索按钮
        self.clear_search_btn = QPushButton("清除")
        self.clear_search_btn.setToolTip("清除搜索条件，显示所有违例")
        self.clear_search_btn.clicked.connect(self.clear_search_filter)
        third_row.addWidget(self.clear_search_btn)
        
        # 搜索统计标签
        self.search_stats_label = QLabel("")
        self.search_stats_label.setStyleSheet("color: #666; font-size: 12px;")
        third_row.addWidget(self.search_stats_label)
        
        third_row.addStretch()
        
        layout.addLayout(third_row)
        
        # 第四行：进度和操作按钮
        fourth_row = QHBoxLayout()
        
        # 进度信息
        self.progress_label = QLabel("进度: 已确认 0/0 (0%)")
        fourth_row.addWidget(self.progress_label)
        
        fourth_row.addStretch()
        
        # 操作按钮
        self.auto_confirm_btn = QPushButton("自动确认")
        self.auto_confirm_btn.setToolTip("根据复位时间和复位区间自动确认违例")
        self.auto_confirm_btn.clicked.connect(self.auto_confirm_violations)
        fourth_row.addWidget(self.auto_confirm_btn)
        
        self.batch_confirm_btn = QPushButton("批量确认")
        self.batch_confirm_btn.setToolTip("批量确认选中的违例")
        self.batch_confirm_btn.clicked.connect(self.batch_confirm_violations)
        fourth_row.addWidget(self.batch_confirm_btn)
        
        self.confirm_all_btn = QPushButton("全部确认")
        self.confirm_all_btn.setToolTip("确认所有待确认的违例")
        self.confirm_all_btn.clicked.connect(self.confirm_all_violations)
        fourth_row.addWidget(self.confirm_all_btn)

        self.apply_history_btn = QPushButton("应用历史")
        self.apply_history_btn.setToolTip("应用历史确认记录到匹配的违例（corner无关，跨corner应用）")
        self.apply_history_btn.clicked.connect(self.apply_historical_confirmations)
        fourth_row.addWidget(self.apply_history_btn)
        
        layout.addLayout(fourth_row)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        return group_box
    
    def create_violation_table(self) -> QGroupBox:
        """创建违例列表表格"""
        group_box = QGroupBox("时序违例列表")
        layout = QVBoxLayout(group_box)

        # 创建性能提示标签
        self.performance_info_label = QLabel("")
        self.performance_info_label.setStyleSheet("color: #666; font-size: 12px; padding: 2px;")
        self.performance_info_label.setVisible(False)
        layout.addWidget(self.performance_info_label)

        # 创建标准表格（用于小数据集）
        self.violation_table = QTableWidget()
        self.violation_table.setColumnCount(8)

        # 设置表头
        headers = ["NUM", "层级路径", "时间(ns)", "检查信息", "状态", "确认人", "确认结果", "操作"]
        self.violation_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.violation_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.violation_table.setAlternatingRowColors(True)
        self.violation_table.setSortingEnabled(True)

        # 设置行高
        self.violation_table.verticalHeader().setDefaultSectionSize(35)
        self.violation_table.verticalHeader().setMinimumSectionSize(35)

        # 设置列宽
        header = self.violation_table.horizontalHeader()
        header.setStretchLastSection(False)
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # NUM
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 层级路径
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # 时间
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # 检查信息
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # 状态
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # 确认人
        header.setSectionResizeMode(6, QHeaderView.Fixed)  # 确认结果
        header.setSectionResizeMode(7, QHeaderView.Fixed)  # 操作

        # 设置固定列宽
        self.violation_table.setColumnWidth(0, 60)   # NUM
        self.violation_table.setColumnWidth(2, 100)  # 时间
        self.violation_table.setColumnWidth(4, 80)   # 状态
        self.violation_table.setColumnWidth(5, 100)  # 确认人
        self.violation_table.setColumnWidth(6, 80)   # 确认结果
        self.violation_table.setColumnWidth(7, 100)  # 操作

        # 创建高性能表格（用于大数据集）
        self.high_performance_table = HighPerformanceTableView(self)
        self.high_performance_table.setVisible(False)

        # 添加到布局
        layout.addWidget(self.violation_table)
        layout.addWidget(self.high_performance_table)

        return group_box
    
    def create_status_bar(self):
        """创建状态栏"""
        # 创建兼容的状态栏类
        class CompatibleStatusBar(QWidget):
            def __init__(self, parent=None):
                super().__init__(parent)
                self.setup_ui()
                
            def setup_ui(self):
                layout = QHBoxLayout(self)
                layout.setContentsMargins(5, 2, 5, 2)
                
                # 主状态标签
                self.main_status_label = QLabel("就绪")
                layout.addWidget(self.main_status_label)
                
                # 添加弹性空间
                layout.addStretch()
                
                # 添加统计信息
                self.stats_label = QLabel("总计: 0条违例 | 已确认: 0条 | 待确认: 0条")
                layout.addWidget(self.stats_label)
                
                # 添加性能信息
                self.perf_label = QLabel("")
                self.perf_label.setStyleSheet("color: #666; font-size: 11px;")
                layout.addWidget(self.perf_label)
                
                # 添加时间信息
                self.time_label = QLabel("")
                layout.addWidget(self.time_label)
                
            def showMessage(self, message, timeout=0):
                """兼容QStatusBar的showMessage方法"""
                self.main_status_label.setText(message)
                if timeout > 0:
                    # 如果指定了超时时间，设置定时器恢复默认状态
                    QTimer.singleShot(timeout, lambda: self.main_status_label.setText("就绪"))
        
        # 创建状态栏实例
        status_bar = CompatibleStatusBar()
        
        # 保存子组件的引用以便其他方法使用
        self.status_label = status_bar.main_status_label
        self.stats_label = status_bar.stats_label
        self.perf_label = status_bar.perf_label
        self.time_label = status_bar.time_label
        
        # 定时更新时间
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time_display)
        self.timer.start(1000)  # 每秒更新
        self.update_time_display()

        return status_bar

    def on_cell_double_clicked(self, row: int, column: int):
        """处理表格单元格双击事件"""
        # 只处理层级路径列（第1列，索引为1）
        if column == 1:
            item = self.violation_table.item(row, column)
            if item:
                hier_path = item.text()
                if hier_path:
                    # 跨平台剪贴板复制
                    success = self._copy_to_clipboard(hier_path)

                    if success:
                        # 显示成功提示消息
                        self.status_label.setText(f"已复制层级路径: {hier_path[:50]}{'...' if len(hier_path) > 50 else ''}")
                    else:
                        # 显示失败提示消息
                        self.status_label.setText(f"复制失败，请手动选择文本: {hier_path[:30]}...")

                    # 使用QTimer在3秒后恢复原状态文本
                    QTimer.singleShot(3000, self.restore_status_text)

    def _copy_to_clipboard(self, text: str) -> bool:
        """跨平台剪贴板复制功能

        Args:
            text: 要复制的文本

        Returns:
            bool: 复制是否成功
        """
        try:
            import platform
            system = platform.system().lower()

            # 方法1: 使用PyQt5剪贴板（主要方法）
            clipboard = QApplication.clipboard()

            # 在Linux下，尝试设置多种剪贴板模式
            if system == 'linux':
                # 设置主剪贴板（Ctrl+V粘贴）
                clipboard.setText(text, clipboard.Clipboard)
                # 设置选择剪贴板（鼠标中键粘贴）
                clipboard.setText(text, clipboard.Selection)

                # 验证复制是否成功
                clipboard_text = clipboard.text(clipboard.Clipboard)
                selection_text = clipboard.text(clipboard.Selection)

                if clipboard_text == text or selection_text == text:
                    print(f"Linux剪贴板复制成功: {text[:50]}...")
                    return True
                else:
                    print(f"Linux剪贴板复制验证失败")
                    # 尝试备用方法
                    return self._copy_to_clipboard_fallback(text)
            else:
                # Windows和macOS使用标准剪贴板
                clipboard.setText(text, clipboard.Clipboard)

                # 验证复制是否成功
                clipboard_text = clipboard.text(clipboard.Clipboard)
                if clipboard_text == text:
                    print(f"{system.title()}剪贴板复制成功: {text[:50]}...")
                    return True
                else:
                    print(f"{system.title()}剪贴板复制验证失败")
                    return False

        except Exception as e:
            print(f"剪贴板复制异常: {str(e)}")
            # 尝试备用方法
            return self._copy_to_clipboard_fallback(text)

    def _copy_to_clipboard_fallback(self, text: str) -> bool:
        """备用剪贴板复制方法（使用系统命令）

        Args:
            text: 要复制的文本

        Returns:
            bool: 复制是否成功
        """
        try:
            import platform
            import subprocess

            system = platform.system().lower()

            if system == 'linux':
                # 尝试使用xclip命令
                try:
                    # 复制到主剪贴板
                    subprocess.run(['xclip', '-selection', 'clipboard'],
                                 input=text.encode('utf-8'),
                                 check=True,
                                 timeout=2)
                    # 复制到选择剪贴板
                    subprocess.run(['xclip', '-selection', 'primary'],
                                 input=text.encode('utf-8'),
                                 check=True,
                                 timeout=2)
                    print(f"Linux xclip复制成功: {text[:50]}...")
                    return True
                except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
                    # 尝试使用xsel命令
                    try:
                        subprocess.run(['xsel', '--clipboard', '--input'],
                                     input=text.encode('utf-8'),
                                     check=True,
                                     timeout=2)
                        subprocess.run(['xsel', '--primary', '--input'],
                                     input=text.encode('utf-8'),
                                     check=True,
                                     timeout=2)
                        print(f"Linux xsel复制成功: {text[:50]}...")
                        return True
                    except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
                        print("Linux备用剪贴板工具不可用")
                        return False

            elif system == 'windows':
                # Windows使用clip命令
                try:
                    subprocess.run(['clip'],
                                 input=text.encode('utf-8'),
                                 check=True,
                                 timeout=2)
                    print(f"Windows clip复制成功: {text[:50]}...")
                    return True
                except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
                    print("Windows clip命令不可用")
                    return False

            elif system == 'darwin':  # macOS
                # macOS使用pbcopy命令
                try:
                    subprocess.run(['pbcopy'],
                                 input=text.encode('utf-8'),
                                 check=True,
                                 timeout=2)
                    print(f"macOS pbcopy复制成功: {text[:50]}...")
                    return True
                except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
                    print("macOS pbcopy命令不可用")
                    return False

            return False

        except Exception as e:
            print(f"备用剪贴板复制异常: {str(e)}")
            return False

    def restore_status_text(self):
        """恢复状态栏文本"""
        if hasattr(self, 'current_case_name') and self.current_case_name:
            self.update_progress_display()
        else:
            self.status_label.setText("就绪")

    def connect_signals(self):
        """连接信号槽"""
        # 数据模型信号 - 使用Qt.QueuedConnection避免死锁
        self.data_model.violation_added.connect(self.on_violation_added, Qt.QueuedConnection)
        self.data_model.violation_updated.connect(self.on_violation_updated, Qt.QueuedConnection)
        self.data_model.confirmation_updated.connect(self.on_confirmation_updated, Qt.QueuedConnection)

        # 界面信号
        self.corner_combo.currentTextChanged.connect(self.on_corner_changed)
        self.case_name_edit.textChanged.connect(self.on_case_name_changed)

        # 标准表格双击事件
        self.violation_table.cellDoubleClicked.connect(self.on_cell_double_clicked)

        # 高性能表格信号
        self.high_performance_table.cell_double_clicked.connect(self.on_high_performance_cell_double_clicked)
        self.high_performance_table.action_button_clicked.connect(self.on_high_performance_action_button_clicked)

    def apply_runsim_theme(self):
        """应用RunSim GUI主题样式"""
        runsim_style = """
            /* 主窗口样式 - 与 Runsim GUI 保持一致 */
            QMainWindow, QDialog {
                background-color: #f5f5f5;
                color: #444;
            }

            /* 分组框样式 */
            QGroupBox {
                font-family: "Microsoft YaHei";
                font-weight: bold;
                border: 2px solid #d0d0d0;
                border-radius: 6px;
                margin-top: 12px;
                padding: 10px;
                background-color: white;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                left: 10px;
                padding: 0 5px;
                color: #444;
            }

            /* 按钮样式 */
            QPushButton {
                background-color: #4a9eff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
                font-family: "Microsoft YaHei";
                min-width: 80px;
            }

            QPushButton:hover {
                background-color: #3d8ced;
            }

            QPushButton:pressed {
                background-color: #3274bf;
            }

            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }

            /* 输入框样式 */
            QLineEdit {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px;
                background: white;
                selection-background-color: #4a9eff;
                font-family: "Microsoft YaHei";
            }

            QLineEdit:focus {
                border: 2px solid #4a9eff;
            }

            /* 下拉框样式 */
            QComboBox {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px;
                background: white;
                font-family: "Microsoft YaHei";
                min-width: 100px;
            }

            QComboBox:focus {
                border: 2px solid #4a9eff;
            }

            QComboBox::drop-down {
                border: none;
                width: 20px;
            }

            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666;
                margin-right: 5px;
            }

            /* 表格样式 */
            QTableWidget {
                background-color: white;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                gridline-color: #e0e0e0;
                font-family: "Microsoft YaHei";
                selection-background-color: #4a9eff;
                selection-color: white;
            }

            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #e0e0e0;
            }

            QTableWidget::item:hover {
                background-color: #f0f8ff;
            }

            QTableWidget::item:selected {
                background-color: #4a9eff;
                color: white;
            }

            QHeaderView::section {
                background-color: #f8f8f8;
                border: 1px solid #d0d0d0;
                padding: 5px;
                font-weight: bold;
                font-family: "Microsoft YaHei";
            }

            /* 进度条样式 */
            QProgressBar {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                text-align: center;
                font-family: "Microsoft YaHei";
            }

            QProgressBar::chunk {
                background-color: #4a9eff;
                border-radius: 3px;
            }

            /* 状态栏样式 */
            QStatusBar {
                background-color: #f0f0f0;
                border-top: 1px solid #d0d0d0;
                color: #666;
                font-family: "Microsoft YaHei";
            }

            /* 工具栏样式 */
            QToolBar {
                background-color: #f8f8f8;
                border-bottom: 1px solid #d0d0d0;
                spacing: 3px;
                padding: 3px;
            }

            QToolBar::separator {
                background-color: #d0d0d0;
                width: 1px;
                margin: 0 5px;
            }
        """
        self.setStyleSheet(runsim_style)

    # Performance Integration Methods
    
    def _initialize_performance_integration(self):
        """Initialize performance integration system"""
        try:
            if PERFORMANCE_INTEGRATION_AVAILABLE and MainWindowIntegrationAdapter:
                self.integration_adapter = MainWindowIntegrationAdapter(self)
                
                # Connect integration signals
                self.integration_adapter.status_message_changed.connect(self._update_status_message)
                self.integration_adapter.progress_updated.connect(self._update_progress)
                self.integration_adapter.table_widget_ready.connect(self._handle_new_table_widget)
                self.integration_adapter.performance_alert.connect(self._handle_performance_alert)
                
                self.performance_integration_enabled = True
                print("✓ Performance integration initialized in main window")
                
            else:
                print("⚠ Performance integration not available, using legacy mode")
                self.performance_integration_enabled = False
                
        except Exception as e:
            print(f"✗ Failed to initialize performance integration: {e}")
            self.performance_integration_enabled = False
    
    def _apply_performance_integration_patches(self):
        """Apply performance integration patches to existing methods"""
        try:
            if (self.performance_integration_enabled and 
                self.integration_adapter and 
                BackwardCompatibilityManager):
                
                self.compatibility_manager = BackwardCompatibilityManager(
                    self, self.integration_adapter
                )
                self.compatibility_manager.apply_integration_patches()
                print("✓ Performance integration patches applied")
                
        except Exception as e:
            print(f"✗ Failed to apply performance integration patches: {e}")
    
    def _update_status_message(self, message: str):
        """Update status message from integration system"""
        if hasattr(self, 'status_label'):
            self.status_label.setText(message)
    
    def _update_progress(self, progress: int, message: str):
        """Update progress from integration system"""
        if hasattr(self, 'progress_bar'):
            self.progress_bar.setVisible(progress < 100)
            self.progress_bar.setValue(progress)
        
        if hasattr(self, 'status_label'):
            if progress < 100:
                self.status_label.setText(f"{message} ({progress}%)")
            else:
                self.status_label.setText("就绪")
    
    def _handle_new_table_widget(self, new_table_widget: QWidget):
        """Handle new optimized table widget from integration system"""
        try:
            # Replace existing table widget with optimized one
            if hasattr(self, 'violation_table_container'):
                # Find the layout containing the table
                layout = self.violation_table_container.layout()
                if layout:
                    # Remove old table widgets
                    for i in reversed(range(layout.count())):
                        child = layout.itemAt(i)
                        if child.widget():
                            child.widget().setParent(None)
                    
                    # Add new optimized table
                    layout.addWidget(new_table_widget)
                    print("✓ Replaced table with optimized version")
                    
        except Exception as e:
            print(f"✗ Failed to handle new table widget: {e}")
    
    def _handle_performance_alert(self, alert_type: str, message: str):
        """Handle performance alerts from integration system"""
        print(f"Performance Alert [{alert_type}]: {message}")
        
        # Show critical alerts to user
        if alert_type in ['memory_critical', 'parsing_error']:
            if hasattr(self, 'status_label'):
                self.status_label.setText(f"Alert: {message}")
    
    def get_performance_integration_status(self) -> Dict:
        """Get performance integration system status"""
        if self.integration_adapter:
            return self.integration_adapter.get_system_health_status()
        else:
            return {
                'integration_enabled': False,
                'reason': 'Integration adapter not available'
            }
    
    def enable_performance_integration_legacy_mode(self):
        """Enable legacy mode for performance integration"""
        if self.integration_adapter:
            self.integration_adapter.enable_legacy_mode()
        self.performance_integration_enabled = False
    
    def try_enable_performance_integration(self):
        """Try to re-enable performance integration"""
        if self.integration_adapter:
            success = self.integration_adapter.try_enable_integration_mode()
            if success:
                self.performance_integration_enabled = True
                return True
        return False

    def select_log_file(self):
        """选择日志文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择时序违例日志文件",
            "",
            "日志文件 (vio_summary.log);;所有文件 (*)"
        )

        if file_path:
            self.load_log_file(file_path)

    def show_regression_batch_dialog(self):
        """显示回归批量扫描对话框"""
        try:
            # 获取默认回归路径
            default_regression_path = os.path.join(os.getcwd(), "regression")

            # 创建回归批量对话框
            dialog = RegressionBatchDialog(self, default_regression_path)
            
            # 显示对话框，不连接信号避免重复处理
            if dialog.exec_() == QDialog.Accepted:
                selected_files = dialog.get_selected_files()
                if selected_files:
                    self.process_regression_files(selected_files)

        except Exception as e:
            QMessageBox.critical(
                self, "错误",
                f"打开回归批量扫描对话框失败：\n{str(e)}"
            )

    def on_regression_files_selected(self, selected_files: List[RegressionFileInfo]):
        """回归文件选择完成处理（已移除，避免重复处理）"""
        # 这个方法已经不再使用，避免重复处理
        pass

    def process_regression_files(self, selected_files: List[RegressionFileInfo]):
        """处理选中的回归文件"""
        try:
            if not selected_files:
                QMessageBox.warning(self, "警告", "没有选择任何文件")
                return

            # 显示确认对话框
            reply = QMessageBox.question(
                self, "确认批量处理",
                f"您选择了 {len(selected_files)} 个文件进行批量处理。\n\n"
                f"处理方式：\n"
                f"• 文件将按 corner_case_seed 格式分页显示\n"
                f"• 每个文件的违例将合并到统一的违例列表中\n"
                f"• 您可以对所有违例进行统一的确认操作\n\n"
                f"是否继续？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply != QMessageBox.Yes:
                return

            # 开始批量处理
            self.start_batch_processing(selected_files)

        except Exception as e:
            QMessageBox.critical(
                self, "错误",
                f"处理回归文件失败：\n{str(e)}"
            )

    def start_batch_processing(self, selected_files: List[RegressionFileInfo]):
        """开始批量处理"""
        try:
            # 清空当前数据
            self.current_violations = []
            self.current_case_name = "回归批量处理"
            self.current_corner = "多个工艺角"
            self.current_file_path = f"批量处理 ({len(selected_files)} 个文件)"

            # 更新文件路径显示
            self.file_path_edit.setText(self.current_file_path)

            # 创建批量处理进度对话框
            progress_dialog = SimpleBatchProgressDialog(
                self,
                f"批量处理 {len(selected_files)} 个文件"
            )
            progress_dialog.update_progress(0, "正在解析文件...")
            progress_dialog.show()

            # 使用自定义的批量文件处理
            self.process_regression_files_sequentially(selected_files, progress_dialog)

        except Exception as e:
            QMessageBox.critical(
                self, "错误",
                f"启动批量处理失败：\n{str(e)}"
            )

    def _process_single_regression_file(self, file_info: RegressionFileInfo) -> Dict:
        """处理单个回归文件"""
        try:
            # 使用现有的解析器解析文件
            parser = VioLogParser()
            violations = parser.parse_log_file(file_info.file_path)

            # 为每个违例添加来源信息和字段映射
            for i, violation in enumerate(violations):
                # 确保解析器输出的大写字段名存在（数据库需要）
                if 'NUM' not in violation:
                    violation['NUM'] = i + 1
                if 'Hier' not in violation:
                    violation['Hier'] = violation.get('hier', '')
                if 'Check' not in violation:
                    violation['Check'] = violation.get('check_info', '')
                if 'Time' not in violation:
                    violation['Time'] = violation.get('time_display', '')
                
                # 字段名映射：为表格显示添加小写字段名
                violation['num'] = violation.get('NUM', i + 1)
                violation['hier'] = violation.get('Hier', '')
                violation['check_info'] = violation.get('Check', '')
                
                # 添加来源信息
                violation['source_file'] = file_info.file_path
                violation['source_subsys'] = file_info.subsys
                violation['source_corner'] = file_info.corner_name
                violation['source_case'] = file_info.case_name
                violation['source_seed'] = file_info.seed
                violation['display_source'] = f"{file_info.corner_name}_{file_info.case_name}_{file_info.seed}"
                
                # 添加默认状态
                violation['status'] = 'pending'
                violation['confirmer'] = ''
                violation['result'] = ''
                
                # 为批量处理模式生成临时ID，用于确认操作
                violation['id'] = f"batch_{file_info.case_name}_{file_info.seed}_{i + 1}"
                violation['temp_id'] = violation['id']  # 保持兼容性

            return {
                'file_info': file_info,
                'violations': violations,
                'violation_count': len(violations)
            }

        except Exception as e:
            return {
                'file_info': file_info,
                'violations': [],
                'violation_count': 0,
                'error': str(e)
            }

    def process_regression_files_sequentially(self, selected_files: List[RegressionFileInfo], progress_dialog):
        """顺序处理回归文件（支持分组显示）"""
        try:
            all_violations = []
            successful_files = 0
            failed_files = 0
            total_files = len(selected_files)

            for i, file_info in enumerate(selected_files):
                try:
                    # 更新进度
                    progress = int((i / total_files) * 100)
                    progress_dialog.update_progress(
                        progress,
                        f"正在处理文件 {i+1}/{total_files}: {file_info.case_name}_{file_info.seed}"
                    )

                    # 处理单个文件
                    result = self._process_single_regression_file(file_info)

                    if 'error' in result:
                        failed_files += 1
                        print(f"处理文件失败: {file_info.file_path} - {result['error']}")
                    else:
                        successful_files += 1
                        violations = result['violations']
                        all_violations.extend(violations)

                except Exception as e:
                    failed_files += 1
                    print(f"处理文件异常: {file_info.file_path} - {str(e)}")

            # 处理完成
            progress_dialog.update_progress(100, "数据组织中...")

            # 组织数据，插入分组标识行
            organized_violations = self.organize_violations_with_groups(all_violations)

            progress_dialog.update_progress(100, "处理完成")

            # 模拟批量处理完成的结果格式
            results = [{
                'successful_files': successful_files,
                'failed_files': failed_files,
                'total_violations': len(organized_violations),
                'violations': organized_violations
            }]

            self.on_batch_processing_completed(results, progress_dialog)

        except Exception as e:
            self.on_batch_processing_failed(str(e), progress_dialog)

    def organize_violations_with_groups(self, all_violations):
        """
        将违例数据按测试用例分组，并插入分组标识行

        Args:
            all_violations: 所有违例数据列表

        Returns:
            organized_violations: 包含分组标识行的有序违例列表
        """
        try:
            if not all_violations:
                return []

            # 1. 按测试用例分组
            groups = {}
            for violation in all_violations:
                # 使用来源信息作为分组键
                case_name = violation.get('source_case', '')
                corner = violation.get('source_corner', '')
                seed = violation.get('source_seed', '')
                subsys = violation.get('source_subsys', '')

                key = (case_name, corner, seed, subsys)
                if key not in groups:
                    groups[key] = []
                groups[key].append(violation)

            # 2. 为每个组创建分组标识行并组织数据
            organized_violations = []

            # 按组名排序，确保显示顺序一致
            sorted_groups = sorted(groups.items(), key=lambda x: (x[0][3], x[0][0], x[0][1], x[0][2]))  # subsys, case, corner, seed

            for (case_name, corner, seed, subsys), violations in sorted_groups:
                # 创建分组标识行
                group_header = self.create_group_header(case_name, corner, seed, subsys, len(violations), violations[0])
                organized_violations.append(group_header)

                # 添加该组的所有违例（保持原有顺序）
                organized_violations.extend(violations)

            print(f"数据组织完成: {len(groups)}个分组, 总计{len(organized_violations)}行（包含分组标识行）")
            return organized_violations

        except Exception as e:
            print(f"组织违例数据失败: {str(e)}")
            # 如果分组失败，返回原始数据
            return all_violations

    def create_group_header(self, case_name, corner, seed, subsys, violation_count, sample_violation):
        """
        创建分组标识行数据

        Args:
            case_name: 用例名
            corner: 工艺角
            seed: 种子
            subsys: 子系统
            violation_count: 该组的违例数量
            sample_violation: 该组的一个样本违例（用于获取来源信息）

        Returns:
            group_header: 分组标识行数据字典
        """
        group_title = f"regr_{case_name}_{corner}_{seed}"

        group_header = {
            # 分组标识字段
            'is_group_header': True,
            'group_title': group_title,
            'group_case_name': case_name,
            'group_corner': corner,
            'group_seed': seed,
            'group_subsys': subsys,
            'group_violation_count': violation_count,

            # 为了与现有数据结构兼容，保留必要字段
            'num': '',  # 空值，不显示序号
            'hier': '',  # 空值
            'check_info': '',  # 空值
            'time_display': '',  # 空值
            'time_fs': 0,  # 时间字段
            'status': 'group_header',  # 特殊状态标识
            'confirmer': '',  # 空值
            'result': '',  # 空值
            'id': f"group_header_{case_name}_{corner}_{seed}",  # 唯一ID

            # 来源信息（与普通违例保持一致）
            'source_file': sample_violation.get('source_file', ''),
            'source_subsys': subsys,
            'source_corner': corner,
            'source_case': case_name,
            'source_seed': seed,
            'display_source': f"{corner}_{case_name}_{seed}",

            # 大写字段名（数据库兼容）
            'NUM': '',
            'Hier': '',
            'Check': '',
            'Time': '',

            # 临时ID
            'temp_id': f"group_header_{case_name}_{corner}_{seed}"
        }

        return group_header

    def on_batch_processing_completed(self, results: List[Dict], progress_dialog):
        """批量处理完成"""
        try:
            progress_dialog.close()

            # 处理结果（新格式）
            if results and len(results) > 0:
                result = results[0]  # 我们的新格式只有一个结果对象
                successful_files = result.get('successful_files', 0)
                failed_files = result.get('failed_files', 0)
                total_violations = result.get('total_violations', 0)
                all_violations = result.get('violations', [])
            else:
                # 兼容旧格式
                all_violations = []
                successful_files = 0
                failed_files = 0
                total_violations = 0

                for result in results:
                    if 'error' in result:
                        failed_files += 1
                        print(f"处理文件失败: {result['file_info'].file_path} - {result['error']}")
                    else:
                        successful_files += 1
                        violations = result['violations']
                        all_violations.extend(violations)
                        total_violations += len(violations)

            # 更新当前违例列表
            self.current_violations = all_violations
            
            # 清除搜索相关数据，重新开始
            self.original_violations = all_violations.copy()
            self.filtered_violations = []
            self.search_filter_text = ""
            if hasattr(self, 'search_edit'):
                self.search_edit.clear()
            if hasattr(self, 'search_stats_label'):
                self.search_stats_label.setText("")

            # 将批量处理的违例数据保存到数据库，以支持自动确认功能
            if all_violations:
                self._save_batch_violations_to_database(all_violations)

            # 先刷新界面显示
            self.refresh_violation_display()
            
            # 自动应用历史确认记录
            self._auto_apply_historical_confirmations_for_batch()

            # 显示处理结果（只显示一次）
            QMessageBox.information(
                self, "批量处理完成",
                f"批量处理完成！\n\n"
                f"成功处理文件: {successful_files}\n"
                f"失败文件: {failed_files}\n"
                f"总违例数: {total_violations:,}\n\n"
                f"违例已加载到主界面，您可以进行确认操作。"
            )

        except Exception as e:
            QMessageBox.critical(
                self, "错误",
                f"处理批量结果失败：\n{str(e)}"
            )

    def _save_batch_violations_to_database(self, violations: List[Dict]):
        """将批量处理的违例数据保存到数据库"""
        try:
            print(f"开始保存 {len(violations)} 条批量违例到数据库")
            
            # 为每个来源文件分别保存违例数据
            violations_by_source = {}
            for violation in violations:
                # 跳过分组标识行
                if violation.get('is_group_header', False):
                    continue
                    
                source_case = violation.get('source_case', 'unknown')
                source_corner = violation.get('source_corner', 'unknown')
                source_file = violation.get('source_file', '')
                
                key = (source_case, source_corner, source_file)
                if key not in violations_by_source:
                    violations_by_source[key] = []
                violations_by_source[key].append(violation)
            
            total_saved = 0
            for (case_name, corner_name, file_path), case_violations in violations_by_source.items():
                try:
                    print(f"正在保存 {case_name}_{corner_name}: {len(case_violations)} 条违例")
                    
                    # 先清除该case的旧数据，避免重复
                    self.data_model.clear_case_data(case_name, corner_name)
                    
                    # 使用数据模型的add_violations方法保存违例
                    self.data_model.add_violations(
                        violations=case_violations,
                        case_name=case_name,
                        corner=corner_name,
                        file_path=file_path
                    )
                    
                    # 验证保存结果
                    saved_violations = self.data_model.get_violations_by_case(case_name, corner_name)
                    actual_saved = len(saved_violations)
                    total_saved += actual_saved
                    
                    print(f"保存 {case_name}_{corner_name}: {actual_saved} 条违例成功")
                    
                except Exception as e:
                    print(f"保存 {case_name}_{corner_name} 失败: {e}")
            
            print(f"总共成功保存 {total_saved} 条批量违例到数据库")
            
            # 保存后，从数据库重新获取违例数据，以获得正确的数据库ID
            self._update_violations_with_database_ids()
            
        except Exception as e:
            print(f"保存批量违例到数据库失败: {e}")
            # 不抛出异常，避免影响主流程
    
    def _update_violations_with_database_ids(self):
        """从数据库重新获取违例数据，更新ID"""
        try:
            # 从数据库获取最新的违例数据
            db_violations = self.data_model.get_violations_by_case(
                self.current_case_name, 
                self.current_corner
            )
            
            if db_violations:
                print(f"从数据库获取到 {len(db_violations)} 条违例记录")
                
                # 更新当前违例列表，使用数据库中的真实ID
                for violation in self.current_violations:
                    # 根据NUM和Hier匹配数据库记录
                    matching_db_violation = next(
                        (v for v in db_violations 
                         if v.get('num') == violation.get('NUM') and 
                            v.get('hier') == violation.get('Hier')), 
                        None
                    )
                    
                    if matching_db_violation:
                        # 使用数据库中的真实ID
                        violation['id'] = matching_db_violation['id']
                        # 更新其他可能的字段
                        violation['status'] = matching_db_violation.get('status', 'pending')
                        violation['confirmer'] = matching_db_violation.get('confirmer', '')
                        violation['result'] = matching_db_violation.get('result', '')
                
                print("成功更新违例ID为数据库ID")
            
        except Exception as e:
            print(f"更新违例ID失败: {e}")

    def on_batch_processing_failed(self, error_message: str, progress_dialog):
        """批量处理失败"""
        progress_dialog.close()
        QMessageBox.critical(
            self, "批量处理失败",
            f"批量处理过程中发生错误：\n{error_message}"
        )

    def refresh_violation_display(self):
        """刷新违例显示"""
        try:
            if not self.current_violations:
                print("没有违例数据需要显示")
                return

            print(f"开始刷新违例显示，共 {len(self.current_violations)} 条记录")

            # 如果是批量处理模式，更新界面控件
            if self.current_case_name == "回归批量处理":
                # 更新用例名称和corner显示
                self.case_name_edit.setText(self.current_case_name)
                if hasattr(self, 'corner_combo'):
                    # 尝试设置corner，如果不存在则添加
                    corner_text = getattr(self, 'current_corner', '')
                    if corner_text and self.corner_combo.findText(corner_text) == -1:
                        self.corner_combo.addItem(corner_text)
                    if corner_text:
                        self.corner_combo.setCurrentText(corner_text)

                # 更新文件路径显示
                if hasattr(self, 'file_path_edit'):
                    self.file_path_edit.setText(getattr(self, 'current_file_path', ''))

            # 更新统计信息
            self.update_progress_display()

            # 获取正确的违例数据：区分批量模式和单文件模式
            if self.current_case_name == "回归批量处理" and hasattr(self, 'current_violations') and self.current_violations:
                # 批量模式：使用内存中的数据
                violations_to_display = self.current_violations
                print(f"批量模式刷新：使用内存中的 {len(violations_to_display)} 条记录")
            else:
                # 单文件模式：从数据库获取最新数据
                violations_to_display = self._get_violations_with_smart_corner_matching()
                print(f"单文件模式刷新：从数据库获取 {len(violations_to_display)} 条记录")
                
                # 确保单文件模式下没有分组头信息
                violations_to_display = [v for v in violations_to_display if not v.get('is_group_header', False)]
                print(f"单文件模式：过滤分组头后剩余 {len(violations_to_display)} 条记录")
                
                # 更新内存中的数据
                self.current_violations = violations_to_display

            # 确保表格已创建（在获取正确数据后）
            if not hasattr(self, 'violation_table') or not self.violation_table:
                print("表格不存在，重新创建")
                # 检查create_violation_table方法是否需要参数
                try:
                    # 尝试不带参数调用（原始方法）
                    self.create_violation_table()
                except TypeError:
                    # 如果失败，说明被集成适配器修改了，需要传入正确的violations_data参数
                    self.create_violation_table(violations_to_display, self)

            # 根据数据量选择合适的显示方式
            violation_count = len(violations_to_display)

            if violation_count > 10000:
                # 大数据集使用高性能表格
                print(f"使用高性能表格显示 {violation_count} 条记录")
                self._use_high_performance_table(violations_to_display)
            else:
                # 小数据集使用标准表格
                print(f"使用标准表格显示 {violation_count} 条记录")
                self._use_standard_table(violations_to_display)

            # 更新状态栏 - 只统计真正的违例，不包括分组标识行
            if hasattr(self, 'status_label'):
                actual_violations = [v for v in self.current_violations if not v.get('is_group_header', False)]
                status_text = f"已加载 {len(actual_violations):,} 条违例记录"
                if self.current_case_name == "回归批量处理":
                    status_text += " (批量处理模式)"
                self.status_label.setText(status_text)

            print("违例显示刷新完成")

        except Exception as e:
            print(f"刷新违例显示失败: {str(e)}")
            import traceback
            traceback.print_exc()
            
            # 如果刷新失败，尝试重新创建表格
            try:
                print("尝试重新创建表格")

                # 获取正确的违例数据用于异常恢复
                if self.current_case_name == "回归批量处理" and hasattr(self, 'current_violations') and self.current_violations:
                    fallback_violations = self.current_violations
                else:
                    fallback_violations = self._get_violations_with_smart_corner_matching()

                # 检查create_violation_table方法是否需要参数
                try:
                    # 尝试不带参数调用（原始方法）
                    self.create_violation_table()
                except TypeError:
                    # 如果失败，说明被集成适配器修改了，需要传入正确的violations_data参数
                    self.create_violation_table(fallback_violations, self)

                if fallback_violations:
                    self._use_standard_table(fallback_violations)
            except Exception as e2:
                print(f"重新创建表格也失败: {str(e2)}")
                import traceback
                traceback.print_exc()

    def load_log_file(self, file_path: str):
        """加载日志文件"""
        try:
            # 清除批量模式的数据，确保单文件模式的纯净性
            if hasattr(self, 'current_violations'):
                self.current_violations = []
            
            # 清除搜索相关数据
            self.original_violations = []
            self.filtered_violations = []
            self.search_filter_text = ""
            if hasattr(self, 'search_edit'):
                self.search_edit.clear()
            if hasattr(self, 'search_stats_label'):
                self.search_stats_label.setText("")
            
            # 清除表格中的所有数据，防止批量模式的分组头残留
            if hasattr(self, 'violation_table') and self.violation_table:
                self.violation_table.setRowCount(0)
                self.violation_table.clearContents()
            
            # 清除高性能表格的数据
            if hasattr(self, 'high_performance_table') and self.high_performance_table:
                self.high_performance_table.update_data([])

            # 更新文件路径显示
            self.file_path_edit.setText(file_path)
            self.current_file_path = file_path

            # 解析用例信息
            dir_path = os.path.dirname(os.path.dirname(file_path))  # 去掉/log/vio_summary.log
            case_info = CaseInfoParser.parse_directory_name(dir_path)

            # 更新用例信息
            self.case_name_edit.setText(case_info['case_name'])
            self.current_case_name = case_info['case_name']

            if case_info['corner']:
                # 找到对应的corner并选中
                index = self.corner_combo.findText(case_info['corner'])
                if index >= 0:
                    self.corner_combo.setCurrentIndex(index)
                    self.current_corner = case_info['corner']
                    print(f"设置corner为: {self.current_corner}")
                else:
                    self.corner_combo.setCurrentIndex(0)
                    self.current_corner = ""
                    print("未找到匹配的corner，设置为空")
            else:
                self.corner_combo.setCurrentIndex(0)
                self.current_corner = ""
                print("没有corner信息，设置为空")

            # 异步解析文件
            self.parse_file_async(file_path)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载文件失败: {str(e)}")

    def parse_file_async(self, file_path: str):
        """智能异步解析文件"""
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)

        # 禁用相关按钮
        self.select_file_btn.setEnabled(False)
        self.auto_confirm_btn.setEnabled(False)

        try:
            # 使用性能优化器分析文件
            self.status_label.setText("正在分析文件并选择最优策略...")
            file_analysis = self.performance_optimizer.analyze_file_performance(file_path)
            
            if file_analysis:
                # 使用智能策略管理器选择策略
                estimated_violations = file_analysis.get('estimated_violations', 0)
                system_capabilities = file_analysis.get('system_capabilities', {})
                performance_level = file_analysis.get('performance_level', 'good')
                
                self.current_strategy = self.strategy_manager.select_strategy_by_violation_count(
                    estimated_violations, system_capabilities, performance_level
                )
                
                # 显示策略选择信息
                strategy_info = f"预估{estimated_violations:,}个违例，使用{self.current_strategy.strategy_name}策略"
                self.status_label.setText(f"策略选择完成: {strategy_info}")
                print(f"策略选择: {self.current_strategy.selection_reason}")
                
                # 根据选择的策略创建解析器
                parser_type = self.current_strategy.parser_type
                if parser_type in ['high_performance_streaming', 'high_performance_async', 'memory_efficient_streaming']:
                    self.async_parser = HighPerformanceAsyncParser(file_path)
                    print(f"使用高性能解析器 (策略: {parser_type})")
                else:
                    # 默认使用异步解析器
                    self.async_parser = AsyncVioLogParser(file_path)
                    print(f"使用标准解析器 (策略: {parser_type})")
                
                # 启用动态策略切换监控
                self.strategy_manager.enable_dynamic_switching(True)
                
            else:
                # 如果分析失败，回退到原有逻辑
                print("文件分析失败，使用回退逻辑")
                self._select_parser_by_file_size(file_path)
        
        except Exception as e:
            print(f"智能策略选择失败: {str(e)}，使用回退逻辑")
            import traceback
            traceback.print_exc()
            self.current_strategy = None  # 确保策略为None，触发回退逻辑
            self._select_parser_by_file_size(file_path)

        # 连接信号
        self.async_parser.progress_updated.connect(self.on_parsing_progress)
        self.async_parser.parsing_completed.connect(self.on_parsing_completed)
        self.async_parser.parsing_failed.connect(self.on_parsing_failed)

        # 开始解析
        strategy_name = self.current_strategy.strategy_name if self.current_strategy else "fallback"
        self.status_label.setText(f"开始解析文件 (策略: {strategy_name})...")
        self.async_parser.start()
    
    def _select_parser_by_file_size(self, file_path: str):
        """根据文件大小选择解析器（回退逻辑）"""
        file_size = os.path.getsize(file_path)
        file_size_mb = file_size / (1024 * 1024)

        # 根据文件大小选择解析策略 - 进一步降低阈值，更早使用高性能模式
        if file_size_mb > 2:  # 大于2MB就使用高性能解析器，避免GUI卡死
            self.status_label.setText(f"检测到大文件 ({file_size_mb:.1f}MB)，使用高性能解析器...")
            self.async_parser = HighPerformanceAsyncParser(file_path)
        else:
            self.status_label.setText(f"使用标准解析器解析文件 ({file_size_mb:.1f}MB)...")
            self.async_parser = AsyncVioLogParser(file_path)

    def on_parsing_progress(self, progress: int, message: str):
        """解析进度更新"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)

    def on_parsing_completed(self, violations: List[Dict]):
        """解析完成"""
        try:
            # 隐藏进度条
            self.progress_bar.setVisible(False)

            # 启用按钮
            self.select_file_btn.setEnabled(True)
            self.auto_confirm_btn.setEnabled(True)

            # 保存违例数据
            self.current_violations = violations

            # 显示性能统计信息
            self._show_performance_stats(violations)

            # 清除旧数据
            if self.current_case_name:
                stored_corner = self.get_stored_corner()
                self.data_model.clear_case_data(self.current_case_name, stored_corner)

            # 添加新数据到数据库
            if violations and self.current_case_name:
                stored_corner = self.get_stored_corner()
                success_count = self.data_model.add_violations(
                    violations, self.current_case_name, stored_corner, self.current_file_path
                )
                print(f"成功添加 {success_count} 条违例记录到数据库")

                # 自动应用历史确认记录（corner无关模式）
                applied_count = self.data_model.apply_historical_confirmations(
                    self.current_case_name, None  # 传入None实现corner无关
                )
                if applied_count > 0:
                    #print(f"自动应用历史确认记录: {applied_count} 条")
                    self.status_label.setText(f"解析完成，共 {len(violations)} 条违例记录，自动应用历史确认 {applied_count} 条")
                else:
                    self.status_label.setText(f"解析完成，共 {len(violations)} 条违例记录")

            # 更新表格显示
            self.update_violation_table()

            # 更新进度显示
            self.update_progress_display()

            # 更新性能信息显示
            self.update_performance_display()
            
            # 自动更新网页数据（如果网页服务正在运行）
            self._auto_update_web_display()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"处理解析结果失败: {str(e)}")

    def _show_performance_stats(self, violations: List[Dict]):
        """显示性能统计信息"""
        try:
            # 获取解析器统计信息
            if hasattr(self.async_parser, 'parser') and hasattr(self.async_parser.parser, 'parse_stats'):
                stats = self.async_parser.parser.parse_stats
                total_lines = stats.get('total_lines', 0)
                parse_time = stats.get('parse_time', 0)

                if parse_time > 0:
                    throughput = len(violations) / parse_time
                    lines_per_sec = total_lines / parse_time

                    print(f"=== 解析性能统计 ===")
                    print(f"总行数: {total_lines:,}")
                    print(f"违例记录: {len(violations):,}")
                    print(f"解析时间: {parse_time:.2f}秒")
                    print(f"处理速度: {lines_per_sec:.0f} 行/秒")
                    print(f"违例吞吐量: {throughput:.0f} 记录/秒")

                    # 性能建议
                    if parse_time > 10:
                        print(f"性能建议: 文件较大，建议考虑分批处理或使用SSD存储")
                    elif throughput < 1000:
                        print(f"性能建议: 吞吐量较低，可能受到磁盘I/O限制")

        except Exception as e:
            print(f"显示性能统计失败: {str(e)}")    
    
    def update_performance_display(self):
        """更新性能信息显示"""
        try:
            stats = self.performance_stats
            if stats['last_record_count'] > 0:
                load_time = stats['last_load_time']
                record_count = stats['last_record_count']
                memory_mb = stats['memory_usage_mb']

                # 计算性能指标
                throughput = record_count / max(load_time, 0.001)  # 避免除零

                # 生成性能信息
                perf_info = f"加载: {load_time:.2f}s | 吞吐量: {throughput:.0f} 记录/s"
                if memory_mb > 0:
                    perf_info += f" | 内存: {memory_mb:.1f}MB"

                # 性能建议
                suggestions = self._get_performance_suggestions(load_time, record_count, memory_mb)
                if suggestions:
                    perf_info += f" | 建议: {suggestions}"

                self.perf_label.setText(perf_info)
            else:
                self.perf_label.setText("")
        except Exception as e:
            print(f"更新性能显示失败: {str(e)}")

    def _get_performance_suggestions(self, load_time: float, record_count: int, memory_mb: float) -> str:
        """获取性能优化建议"""
        suggestions = []

        # 加载时间建议
        if load_time > 5.0:
            suggestions.append("加载较慢，建议使用筛选功能")
        elif load_time > 2.0:
            suggestions.append("可考虑分批处理")

        # 记录数量建议
        if record_count > 10000:
            suggestions.append("数据量大，建议使用高性能模式")
        elif record_count > 5000:
            suggestions.append("建议启用分页显示")

        # 内存使用建议
        if memory_mb > 500:
            suggestions.append("内存占用较高")

        return " | ".join(suggestions[:2])  # 最多显示2个建议，避免状态栏过长

    def on_parsing_failed(self, error_message: str):
        """解析失败"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 启用按钮
        self.select_file_btn.setEnabled(True)

        # 显示错误 - 使用安全消息框，避免弹窗
        self.status_label.setText("解析失败")
        print(f"解析失败: {error_message}")

        # 在状态栏显示错误信息而不是弹窗
        self.status_label.setText(f"解析失败: {error_message[:50]}...")
        QTimer.singleShot(5000, lambda: self.status_label.setText("就绪"))

    def update_violation_table(self):
        """更新违例表格显示"""
        if not self.current_case_name:
            self._clear_all_tables()
            # 清除搜索相关数据
            self.original_violations = []
            self.filtered_violations = []
            self.search_stats_label.setText("")
            return

        # 智能获取违例记录：批量模式使用内存数据，单文件模式使用数据库数据
        if self.current_case_name == "回归批量处理" and hasattr(self, 'current_violations') and self.current_violations:
            violations = self.current_violations
            # 过滤掉分组标识行，只统计真正的违例
            actual_violations = [v for v in violations if not v.get('is_group_header', False)]
            print(f"批量模式：从内存获取 {len(actual_violations)} 条违例记录（总行数：{len(violations)}，包含{len(violations) - len(actual_violations)}个分组标识行）")
        else:
            # 单文件模式：清除批量模式的内存数据，从数据库获取违例记录
            if hasattr(self, 'current_violations'):
                # 检查是否有分组标识行，如果有则清除（说明是从批量模式切换过来的）
                has_group_headers = any(v.get('is_group_header', False) for v in self.current_violations)
                if has_group_headers:
                    print("检测到分组标识行，清除批量模式数据")
                    self.current_violations = []

            # 从数据库获取违例记录 - 使用智能corner匹配
            violations = self._get_violations_with_smart_corner_matching()
            actual_violations = violations  # 数据库中不包含分组标识行
            print(f"单文件模式：从数据库获取 {len(violations)} 条违例记录")

        # 更新当前违例数据并备份原始数据
        self.current_violations = violations
        self.original_violations = violations.copy()
        self.filtered_violations = []
        
        # 如果有搜索过滤条件，应用过滤
        if self.search_filter_text:
            self.apply_search_filter()
            violations = self.current_violations  # 使用过滤后的数据
        else:
            self.search_stats_label.setText("")

        # 根据数据量和策略选择表格类型，并记录性能统计
        import time
        start_time = time.time()

        # 使用策略配置决定显示模式
        if self.current_strategy:
            try:
                display_mode = self.current_strategy.display_mode
                use_pagination = self.current_strategy.use_pagination
                page_size = self.current_strategy.page_size

                print(f"使用策略配置: 显示模式={display_mode}, 分页={use_pagination}, 页面大小={page_size}")

                # 根据策略选择表格类型
                if display_mode in ['high_performance_table', 'virtual_table_with_lazy_loading', 'paginated_virtual_table']:
                    self._use_high_performance_table(violations)
                else:
                    self._use_standard_table(violations)
            except Exception as e:
                print(f"策略配置应用失败: {e}，使用回退逻辑")
                # 策略配置有问题，使用回退逻辑
                if len(violations) > self.performance_threshold:
                    self._use_high_performance_table(violations)
                else:
                    self._use_standard_table(violations)
        else:
            # 回退到原有逻辑
            print(f"没有策略配置，使用回退逻辑: 违例数量={len(violations)}, 阈值={self.performance_threshold}")
            if len(violations) > self.performance_threshold:
                print("使用高性能表格（回退逻辑）")
                self._use_high_performance_table(violations)
            else:
                print("使用标准表格（回退逻辑）")
                self._use_standard_table(violations)

        # 更新性能统计
        load_time = time.time() - start_time
        self.performance_stats.update({
            'last_load_time': load_time,
            'last_record_count': len(violations),
            'memory_usage_mb': self._get_memory_usage()
        })

        # 监控策略性能
        if self.current_strategy:
            current_metrics = {
                'load_time': load_time,
                'memory_usage_mb': self.performance_stats['memory_usage_mb'],
                'record_count': len(violations),
                'ui_response_time': load_time,  # 简化指标
                'error_rate': 0.0  # 暂时设为0
            }
            
            # 检查是否需要触发回退
            self._check_strategy_performance(current_metrics)

        # 性能警告
        if load_time > 1.0:  # 超过1秒显示警告
            print(f"表格加载耗时: {load_time:.2f}秒，记录数: {len(violations)}")
            strategy_info = f" (策略: {self.current_strategy.strategy_name})" if self.current_strategy else ""
            self.performance_info_label.setText(
                f"表格加载耗时 {load_time:.2f}秒{strategy_info}，建议使用筛选功能减少显示数据量"
            )
            self.performance_info_label.setVisible(True)

    def _check_strategy_performance(self, current_metrics: Dict):
        """检查策略性能并触发回退机制"""
        try:
            load_time = current_metrics.get('load_time', 0)
            memory_usage = current_metrics.get('memory_usage_mb', 0)
            
            # 检查是否需要触发回退
            trigger_condition = None
            
            if memory_usage > 800:  # 内存使用超过800MB
                trigger_condition = 'memory_pressure'
            elif load_time > 15:  # 加载时间超过15秒
                trigger_condition = 'processing_timeout'
            elif load_time > 5 and memory_usage > 500:  # 综合性能问题
                trigger_condition = 'performance_degradation'
            
            if trigger_condition:
                print(f"检测到性能问题: {trigger_condition}, 尝试触发回退")
                fallback_strategy = self.strategy_manager.trigger_fallback(trigger_condition, current_metrics)
                
                if fallback_strategy:
                    self.current_strategy = fallback_strategy
                    print(f"已切换到回退策略: {fallback_strategy.strategy_name}")
                    self.status_label.setText(f"性能优化: 已切换到{fallback_strategy.strategy_name}策略")
                    
                    # 可以在这里重新渲染表格，但为了避免无限循环，暂时���记录
                    
        except Exception as e:
            print(f"策略性能检查失败: {str(e)}")

    def _get_memory_usage(self):
        """获取当前内存使用量（MB）"""
        try:
            import psutil
            import os
            process = psutil.Process(os.getpid())
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            return 0

    def _clear_all_tables(self):
        """清空所有表格"""
        self.violation_table.setRowCount(0)
        self.violation_table.clearContents()
        self.high_performance_table.update_data([])
        self.performance_info_label.setVisible(False)
        
        # 清理选中状态
        if hasattr(self.high_performance_table, 'selected_violations'):
            self.high_performance_table.selected_violations.clear()
        if hasattr(self.high_performance_table, 'select_all_state'):
            self.high_performance_table.select_all_state = False
        
        # 清除搜索相关数据
        self.original_violations = []
        self.filtered_violations = []
        if hasattr(self, 'search_stats_label'):
            self.search_stats_label.setText("")

    def _use_high_performance_table(self, violations):
        """使用高性能表格显示大数据集"""
        print(f"使用高性能表格显示 {len(violations)} 条记录")

        # 隐藏标准表格，显示高性能表格
        self.violation_table.setVisible(False)
        self.high_performance_table.setVisible(True)

        # 显示性能提示
        self.performance_info_label.setText(
            f"检测到大数据集({len(violations)}条记录)，已启用高性能模式以提升加载速度"
        )
        self.performance_info_label.setVisible(True)

        # 更新高性能表格数据
        self.high_performance_table.update_data(violations)
        self.use_high_performance_table = True
        
        # 显示搜索按钮
        if hasattr(self, 'search_btn'):
            self.search_btn.setVisible(True)
            # 更新搜索框提示文本
            if hasattr(self, 'search_edit'):
                self.search_edit.setPlaceholderText("输入层级路径关键字，点击搜索按钮或按回车键...")
                self.search_edit.setToolTip("输入层级路径中的关键字，点击搜索按钮或按回车键进行过滤")

    def _use_standard_table(self, violations):
        """使用标准表格显示小数据集"""
        print(f"使用标准表格显示 {len(violations)} 条记录")

        # 显示标准表格，隐藏高性能表格
        self.violation_table.setVisible(True)
        self.high_performance_table.setVisible(False)
        self.performance_info_label.setVisible(False)

        # 清理表格内容，确保没有残留的控件
        self.violation_table.clearContents()
        self.violation_table.clearSpans()  # 清除所有单元格合并
        
        # 设置表格行数
        self.violation_table.setRowCount(len(violations))
        self.use_high_performance_table = False
        
        # 隐藏搜索按钮
        if hasattr(self, 'search_btn'):
            self.search_btn.setVisible(False)
            # 更新搜索框提示文本
            if hasattr(self, 'search_edit'):
                self.search_edit.setPlaceholderText("输入层级路径关键字进行过滤...")
                self.search_edit.setToolTip("输入层级路径中的关键字，支持实时模糊匹配")

        # 填充标准表格数据
        self._fill_standard_table(violations)

    def _fill_standard_table(self, violations):
        """填充标准表格数据（支持分组显示）"""
        import time
        start_time = time.time()

        # 批量处理：预先计算所有需要的数据，减少重复计算
        processed_data = []
        for violation in violations:
            status = violation.get('status', 'pending')
            is_confirmed = status in ['confirmed', 'ignored']
            is_group_header = violation.get('is_group_header', False)
            time_ns = violation.get('time_fs', 0) / 1000000

            processed_data.append({
                'violation': violation,
                'status': status,
                'is_confirmed': is_confirmed,
                'is_group_header': is_group_header,
                'time_ns': time_ns
            })

        # 定义样式颜色
        gray_text_color = QColor(128, 128, 128)  # 灰色文字
        normal_text_color = QColor(0, 0, 0)      # 正常黑色文字
        group_bg_color = QColor(230, 240, 250)   # 分组标识行背景色
        group_text_color = QColor(0, 50, 100)    # 分组标识行文字色

        # 批量设置表格数据，减少单次操作开销
        for row, data in enumerate(processed_data):
            violation = data['violation']
            status = data['status']
            is_confirmed = data['is_confirmed']
            is_group_header = data['is_group_header']
            time_ns = data['time_ns']

            if is_group_header:
                # 渲染分组标识行
                self._render_group_header_row(row, violation, group_bg_color, group_text_color)
            else:
                # 渲染普通违例行
                self._render_normal_violation_row(row, violation, status, is_confirmed, time_ns, gray_text_color)

        # 性能统计
        load_time = time.time() - start_time
        print(f"标准表格填充完成，耗时: {load_time:.3f}秒，行数: {len(processed_data)}")

    def _render_group_header_row(self, row, group_data, bg_color, text_color):
        """渲染分组标识行"""
        try:
            # 先清除该行所有可能存在的控件，特别是操作列的按钮
            for col in range(8):  # 清除所有8列
                self.violation_table.setCellWidget(row, col, None)
                self.violation_table.setItem(row, col, None)
            
            # 合并所有列的单元格
            self.violation_table.setSpan(row, 0, 1, 8)  # 跨越8列

            # 创建分组标题项
            group_title = group_data.get('group_title', '')
            violation_count = group_data.get('group_violation_count', 0)
            display_text = f"📁 {group_title} ({violation_count}条违例)"

            group_item = QTableWidgetItem(display_text)
            group_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)

            # 设置分组标识行的特殊样式
            group_item.setBackground(bg_color)
            group_item.setForeground(text_color)
            font = group_item.font()
            font.setBold(True)
            font.setPointSize(font.pointSize() + 1)
            group_item.setFont(font)

            # 设置为不可编辑和不可选择
            group_item.setFlags(group_item.flags() & ~Qt.ItemIsEditable & ~Qt.ItemIsSelectable)

            self.violation_table.setItem(row, 0, group_item)

        except Exception as e:
            print(f"渲染分组标识行失败 (行 {row}): {e}")

    def _render_normal_violation_row(self, row, violation, status, is_confirmed, time_ns, gray_text_color):
        """渲染普通违例行"""
        try:
            # 安全检查：如果是分组头，不应该调用这个方法
            if violation.get('is_group_header', False):
                print(f"警告: 分组头被错误地传入普通违例行渲染方法，行 {row}")
                return
            # NUM
            num_item = QTableWidgetItem(str(violation.get('num', '')))
            num_item.setTextAlignment(Qt.AlignCenter)
            if is_confirmed:
                num_item.setForeground(gray_text_color)
            self.violation_table.setItem(row, 0, num_item)

            # 层级路径
            hier_text = violation.get('hier', '')
            hier_item = QTableWidgetItem(hier_text)
            hier_item.setToolTip(hier_text)  # 添加悬停提示
            if is_confirmed:
                hier_item.setForeground(gray_text_color)
            self.violation_table.setItem(row, 1, hier_item)

            # 时间(ns) - 优先使用数据库中存储的格式化时间显示
            time_display = violation.get('time_display', '')
            if time_display:
                if time_display.endswith('FS'):
                    time_ns = float(time_display[:-3]) / 1000000
                elif time_display.endswith('PS'):
                    time_ns = float(time_display[:-3]) / 1000
                else:
                    time_ns = float(time_display[:-3])
                time_text = f"{time_ns:.3f}"
            else:
                # 回退到计算方式（兼容旧数据）
                time_ns = violation.get('time_fs', 0) / 1000000
                time_text = f"{time_ns:.3f}"
            time_item = QTableWidgetItem(time_text)
            time_item.setTextAlignment(Qt.AlignCenter)
            if is_confirmed:
                time_item.setForeground(gray_text_color)
            self.violation_table.setItem(row, 2, time_item)

            # 检查信息
            check_info_text = violation.get('check_info', '')
            check_item = QTableWidgetItem(check_info_text)
            check_item.setToolTip(check_info_text)  # 添加悬停提示
            if is_confirmed:
                check_item.setForeground(gray_text_color)
            self.violation_table.setItem(row, 3, check_item)

            # 状态
            status_item = QTableWidgetItem(self.get_status_display(status))
            status_item.setTextAlignment(Qt.AlignCenter)

            # 设置状态颜色（保持原有的背景色，但文字使用对应颜色）
            if status == 'confirmed':
                status_item.setBackground(QColor(144, 238, 144))  # 浅绿色背景
                status_item.setForeground(QColor(0, 100, 0))     # 深绿色文字
            elif status == 'ignored':
                status_item.setBackground(QColor(255, 182, 193))  # 浅红色背景
                status_item.setForeground(QColor(139, 0, 0))     # 深红色文字
            else:
                status_item.setBackground(QColor(255, 255, 224))  # 浅黄色背景
                status_item.setForeground(QColor(184, 134, 11))  # 深黄色文字

            self.violation_table.setItem(row, 4, status_item)

            # 确认人
            confirmer_item = QTableWidgetItem(violation.get('confirmer', ''))
            confirmer_item.setTextAlignment(Qt.AlignCenter)
            if is_confirmed:
                confirmer_item.setForeground(gray_text_color)
            self.violation_table.setItem(row, 5, confirmer_item)

            # 确认结果
            result = violation.get('result', '')
            result_item = QTableWidgetItem(self.get_result_display(result))
            result_item.setTextAlignment(Qt.AlignCenter)
            if is_confirmed:
                result_item.setForeground(gray_text_color)
            self.violation_table.setItem(row, 6, result_item)

            # 操作按钮 - 分组头不创建任何按钮
            if violation.get('is_group_header', False):
                # 分组头不需要操作按钮，跳过
                pass
            elif status == 'pending':
                confirm_btn = QPushButton("确认")
                confirm_btn.setMaximumWidth(80)
                # 使用安全的按钮连接方式，避免lambda闭包问题
                confirm_btn.setProperty('violation_id', violation.get('id'))
                confirm_btn.setProperty('action_type', 'pending')
                confirm_btn.clicked.connect(self.handle_standard_table_button_click)
                self.violation_table.setCellWidget(row, 7, confirm_btn)
            else:
                edit_btn = QPushButton("编辑")
                edit_btn.setMaximumWidth(80)
                # 为已确认条目的按钮也应用灰色样式
                edit_btn.setStyleSheet("""
                    QPushButton {
                        color: #808080;
                        background-color: #f0f0f0;
                        border: 1px solid #c0c0c0;
                    }
                    QPushButton:hover {
                        background-color: #e0e0e0;
                    }
                """)
                # 使用安全的按钮连接方式，避免lambda闭包问题
                edit_btn.setProperty('violation_id', violation.get('id'))
                edit_btn.setProperty('action_type', 'confirmed')
                edit_btn.clicked.connect(self.handle_standard_table_button_click)
                self.violation_table.setCellWidget(row, 7, edit_btn)

        except Exception as e:
            print(f"渲染普通违例行失败 (行 {row}): {e}")

    def on_high_performance_cell_double_clicked(self, row, column):
        """处理高性能表格单元格双击事件"""
        # 只处理层级路径列（第1列，索引为1）
        if column == 1:
            # 获取实际行索引（考虑分页偏移）
            actual_row = self.high_performance_table.current_page * self.high_performance_table.page_size + row
            violation = self.high_performance_table.model.get_violation_at_row(actual_row)

            if violation:
                hier_path = violation.get('hier', '')
                if hier_path:
                    # 跨平台剪贴板复制
                    success = self._copy_to_clipboard(hier_path)

                    if success:
                        # 显示成功提示消息
                        self.status_label.setText(f"已复制层级路径: {hier_path[:50]}{'...' if len(hier_path) > 50 else ''}")
                    else:
                        # 显示失败提示消息
                        self.status_label.setText(f"复制失败，请手动选择文本: {hier_path[:30]}...")

                    # 使用QTimer在3秒后恢复原状态文本
                    QTimer.singleShot(3000, self.restore_status_text)

    def on_high_performance_action_button_clicked(self, violation_id, action_type):
        """处理高性能表格中的按钮点击事件"""
        try:
            # 根据操作类型执行相应操作
            if action_type == 'pending':
                self.confirm_single_violation(violation_id)
            else:
                self.edit_confirmation(violation_id)
        except Exception as e:
            # 静默处理错误，避免弹窗
            print(f"处理高性能表格按钮点击失败: {e}")
            if hasattr(self, 'status_label'):
                self.status_label.setText(f"操作失败")
                QTimer.singleShot(2000, lambda: self.status_label.setText("就绪"))

    def _get_violations_with_smart_corner_matching(self) -> List[Dict]:
        """使用智能corner匹配获取违例记录

        Returns:
            List[Dict]: 违例记录列表
        """
        if not self.current_case_name:
            return []

        # 智能corner匹配：首先尝试当前corner，如果没找到则尝试default corner
        current_corner = self.current_corner if self.current_corner else "default"
        corners_to_try = [current_corner] if current_corner != "default" else ["default"]

        # 如果当前corner不是default，则在找不到记录时回退到default
        if current_corner != "default":
            corners_to_try.append("default")

        for try_corner in corners_to_try:
            print(f"尝试在corner '{try_corner}' 中查询违例记录...")
            violations = self.data_model.get_violations_by_case(self.current_case_name, try_corner)

            if violations:
                # 过滤掉分组头信息，确保单文件模式的纯净性
                filtered_violations = [v for v in violations if not v.get('is_group_header', False)]
                if try_corner != current_corner:
                    print(f"在corner '{current_corner}' 中未找到记录，回退到corner '{try_corner}' 找到 {len(filtered_violations)} 条违例")
                else:
                    print(f"在corner '{try_corner}' 中找到 {len(filtered_violations)} 条违例记录")
                return filtered_violations
            else:
                print(f"在corner '{try_corner}' 中未找到违例记录")

        print("在所有可能的corner中都没有找到违例记录")
        return []

    def _get_violations_smart(self, case_name: str = None, corner: str = None) -> List[Dict]:
        """智能获取违例记录的通用方法

        Args:
            case_name: 用例名称，如果为None则使用当前用例
            corner: 指定corner，如果为None则使用当前corner

        Returns:
            List[Dict]: 违例记录列表
        """
        if case_name is None:
            case_name = self.current_case_name
        if not case_name:
            return []

        # 如果是批量处理模式（current_case_name为"回归批量处理"），优先使用内存中的数据
        if case_name == "回归批量处理" and hasattr(self, 'current_violations') and self.current_violations:
            actual_violations = [v for v in self.current_violations if not v.get('is_group_header', False)]
            print(f"批量处理模式：从内存获取 {len(actual_violations)} 条违例记录（过滤掉分组标识行）")
            return self.current_violations  # 返回完整数据，包含分组标识行，供表格显示使用

        if corner is None:
            corner = self.current_corner if self.current_corner else "default"

        # 智能corner匹配
        corners_to_try = [corner] if corner != "default" else ["default"]
        if corner != "default":
            corners_to_try.append("default")

        for try_corner in corners_to_try:
            violations = self.data_model.get_violations_by_case(case_name, try_corner)
            if violations:
                # 过滤掉分组头信息，确保单文件模式的纯净性
                filtered_violations = [v for v in violations if not v.get('is_group_header', False)]
                return filtered_violations

        return []

    def get_stored_corner(self) -> str:
        """获取数据存储时使用的corner

        Returns:
            str: 存储时使用的corner名称
        """
        # 如果用户已经选择了具体的corner，优先使用用户选择的corner
        if self.current_corner:
            return self.current_corner

        # 如果当前文件路径中包含corner信息，使用解析出的corner
        if self.current_file_path:
            dir_path = os.path.dirname(os.path.dirname(self.current_file_path))
            case_info = CaseInfoParser.parse_directory_name(dir_path)
            if case_info['corner']:
                return case_info['corner']

        # 否则使用default
        return "default"

    def get_display_corner(self) -> str:
        """获取显示用的corner名称（用于文件名等）

        Returns:
            str: 显示用的corner名称
        """
        if self.current_corner:
            return self.current_corner

        # 如果没有选择corner，使用存储的corner
        stored_corner = self.get_stored_corner()
        return stored_corner if stored_corner != "default" else "default"

    def get_status_display(self, status: str) -> str:
        """获取状态显示文本"""
        status_map = {
            'pending': '待确认',
            'confirmed': '已确认',
            'ignored': '已忽略'
        }
        return status_map.get(status, status)

    def get_result_display(self, result: str) -> str:
        """获取结果显示文本"""
        result_map = {
            'pass': '通过',
            'issue': '有问题',
            '': ''
        }
        return result_map.get(result, result)

    def validate_reset_interval(self, interval_text: str) -> tuple:
        """验证复位区间输入格式

        Args:
            interval_text: 区间文本，格式如"5000~6000"

        Returns:
            tuple: (is_valid, start_time, end_time, error_message)
        """
        if not interval_text.strip():
            return True, None, None, ""  # 空输入是有效的（可选字段）

        # 检查是否包含~符号
        if '~' not in interval_text:
            return False, None, None, "复位区间格式错误，请使用格式：开始时间~结束时间"

        # 分割区间
        parts = interval_text.split('~')
        if len(parts) != 2:
            return False, None, None, "复位区间格式错误，请使用格式：开始时间~结束时间"

        try:
            start_time = float(parts[0].strip())
            end_time = float(parts[1].strip())

            if start_time < 0 or end_time < 0:
                return False, None, None, "时间值不能为负数"

            if start_time >= end_time:
                return False, None, None, "开始时间必须小于结束时间"

            return True, start_time, end_time, ""

        except ValueError:
            return False, None, None, "时间值格式错误，请输入数字"

    def auto_confirm_violations(self):
        """自动确认违例"""
        if not self.current_case_name:
            QMessageBox.warning(self, "警告", "请先选择日志文件")
            return



        # 获取复位时间
        reset_time_ns = None
        reset_time_text = self.reset_time_edit.text().strip()
        if reset_time_text:
            try:
                reset_time_ns = float(reset_time_text)
                if reset_time_ns <= 0:
                    QMessageBox.warning(self, "警告", "复位时间必须大于0")
                    return
            except ValueError:
                QMessageBox.warning(self, "警告", "复位时间格式错误，请输入数字")
                return

        # 获取复位区间
        reset_interval_start = None
        reset_interval_end = None
        interval_text = self.reset_interval_edit.text().strip()
        if interval_text:
            is_valid, start_time, end_time, error_msg = self.validate_reset_interval(interval_text)
            if not is_valid:
                QMessageBox.warning(self, "警告", error_msg)
                return
            reset_interval_start = start_time
            reset_interval_end = end_time

        # 检查是否至少输入了一个条件
        if reset_time_ns is None and reset_interval_start is None:
            QMessageBox.warning(self, "警告", "请至少输入复位时间或复位区间")
            return

        # 如果是批量处理模式，使用内存中的数据进行自动确认
        if self.current_case_name == "回归批量处理" and hasattr(self, 'current_violations') and self.current_violations:
            confirmed_count = self._auto_confirm_batch_violations(reset_time_ns, reset_interval_start, reset_interval_end)
        else:
            # 执行数据库自动确认
            corner = self.current_corner if self.current_corner else "default"
            confirmed_count = self.data_model.auto_confirm_by_reset_time_and_interval(
                self.current_case_name, corner, reset_time_ns, reset_interval_start, reset_interval_end
            )

        if confirmed_count > 0:
            QMessageBox.information(self, "成功", f"已自动确认 {confirmed_count} 条违例记录")
            
            # 批量模式需要刷新违例显示而不是更新表格
            if self.current_case_name == "回归批量处理":
                print("批量模式：刷新违例显示")
                
                # 验证数据状态
                confirmed_count_check = sum(1 for v in self.current_violations if v.get('status') == 'confirmed')
                print(f"刷新前验证：内存中已确认违例数 = {confirmed_count_check}")
                
                self.refresh_violation_display()
                
                # 强制刷新高性能表格的当前页面
                if hasattr(self, 'high_performance_table') and self.high_performance_table.isVisible():
                    print("强制刷新高性能表格当前页面")
                    self.high_performance_table.refresh_current_page()
                    
                    # 验证表格数据模型
                    if hasattr(self.high_performance_table, 'model'):
                        model_data = self.high_performance_table.model._data
                        model_confirmed = sum(1 for v in model_data if v.get('status') == 'confirmed')
                        print(f"表格模型中已确认违例数 = {model_confirmed}")
                
                print("批量模式界面刷新完成")
            else:
                print("单文件模式：更新违例表格")
                self.update_violation_table()
            
            self.update_progress_display()
        else:
            QMessageBox.information(self, "提示", "没有找到需要自动确认的违例记录")

    def _auto_confirm_batch_violations(self, reset_time_ns: float = None, 
                                     reset_interval_start: float = None, 
                                     reset_interval_end: float = None) -> int:
        """批量处理模式下的自动确认"""
        try:
            confirmed_count = 0
            
            print(f"批量自动确认参数 - 复位时间: {reset_time_ns}ns, 复位区间: {reset_interval_start}-{reset_interval_end}ns")
            print(f"当前违例总数: {len(self.current_violations)}")
            
            # 统计当前状态
            status_count = {}
            for v in self.current_violations:
                status = v.get('status', 'pending')
                status_count[status] = status_count.get(status, 0) + 1
            print(f"确认前状态统计: {status_count}")
            
            for i, violation in enumerate(self.current_violations):
                current_status = violation.get('status', 'pending')
                if current_status != 'pending':
                    continue  # 跳过已确认的违例
                
                # 获取违例时间（飞秒）
                time_fs = violation.get('time_fs', 0)
                time_ns = time_fs / 1000000  # 转换为纳秒
                
                should_confirm = False
                
                # 检查复位时间条件
                if reset_time_ns is not None and time_ns <= reset_time_ns:
                    should_confirm = True
                    #print(f"违例 {i} 符合复位时间条件: {time_ns:.3f}ns <= {reset_time_ns}ns")
                
                # 检查复位区间条件
                if (reset_interval_start is not None and reset_interval_end is not None and 
                    reset_interval_start <= time_ns <= reset_interval_end):
                    should_confirm = True
                    #print(f"违例 {i} 符合复位区间条件: {reset_interval_start}ns <= {time_ns:.3f}ns <= {reset_interval_end}ns")
                
                if should_confirm:
                    # 构建确认理由（与单文件模式保持一致）
                    reason_parts = []
                    if reset_time_ns is not None:
                        reason_parts.append(f"复位期间时序违例（<= {reset_time_ns}ns）")
                    if reset_interval_start is not None and reset_interval_end is not None:
                        reason_parts.append(f"复位区间内时序违例（{reset_interval_start}ns~{reset_interval_end}ns）")
                    
                    reason = "，".join(reason_parts) + "，可以忽略"
                    
                    # 更新违例状态（与单文件模式保持一致）
                    old_status = violation['status']
                    violation['status'] = 'confirmed'
                    violation['confirmer'] = '系统自动'  # 与单文件模式保持一致
                    violation['result'] = 'pass'
                    violation['reason'] = reason  # 添加确认理由
                    
                    #print(f"更新违例 {i} 状态: {old_status} -> {violation['status']}")
                    #print(f"确认人: {violation['confirmer']}, 确认理由: {reason}")
                    
                    # 如果违例有数据库ID，也更新数据库记录
                    if 'id' in violation and isinstance(violation['id'], int):
                        try:
                            self.data_model.update_confirmation(
                                violation['id'],
                                status='confirmed',
                                confirmer='系统自动',  # 与单文件模式保持一致
                                result='pass',
                                reason=reason,  # 使用动态生成的理由
                                is_auto=True
                            )
                            #print(f"数据库更新成功，ID: {violation['id']}")
                        except Exception as e:
                            print(f"更新数据库确认记录失败: {e}")
                    
                    confirmed_count += 1
                    #print(f"自动确认批量违例 - Time: {time_ns:.3f}ns, Hier: {violation.get('hier', '')}")
            
            # 统计确认后状态
            status_count_after = {}
            for v in self.current_violations:
                status = v.get('status', 'pending')
                status_count_after[status] = status_count_after.get(status, 0) + 1
            print(f"确认后状态统计: {status_count_after}")
            
            print(f"批量自动确认完成，共确认 {confirmed_count} 条违例")
            return confirmed_count
            
        except Exception as e:
            print(f"批量自动确认失败: {e}")
            import traceback
            traceback.print_exc()
            return 0

    def handle_standard_table_button_click(self):
        """处理标准表格中的按钮点击（安全版本）"""
        try:
            # 获取发送信号的按钮
            button = self.sender()
            if not button:
                return

            # 从按钮属性中获取数据
            violation_id = button.property('violation_id')
            action_type = button.property('action_type')

            # 验证数据有效性
            if not violation_id:
                print(f"标准表格按钮缺少violation_id属性")
                return

            # 根据操作类型执行相应操作
            if action_type == 'pending':
                self.confirm_single_violation(violation_id)
            else:
                self.edit_confirmation(violation_id)

        except Exception as e:
            # 静默处理错误，绝对不显示弹窗
            print(f"处理标准表格按钮点击失败: {e}")
            if hasattr(self, 'status_label'):
                self.status_label.setText(f"操作失败")
                QTimer.singleShot(2000, lambda: self.status_label.setText("就绪"))

    def confirm_single_violation(self, violation_id):
        """确认单个违例（支持字符串和整数ID）"""
        if not violation_id:
            return

        try:
            # 获取违例信息 - 使用智能corner匹配
            violations = self._get_violations_smart()
            
            # 支持字符串ID（批量处理）和整数ID（单文件处理）
            if isinstance(violation_id, str):
                violation = next((v for v in violations if str(v.get('id', '')) == violation_id), None)
            else:
                violation = next((v for v in violations if v.get('id') == violation_id), None)

            if not violation:
                # 静默处理，避免弹窗
                print(f"找不到违例记录，ID: {violation_id}")
                if hasattr(self, 'status_label'):
                    self.status_label.setText("找不到违例记录")
                    QTimer.singleShot(3000, lambda: self.status_label.setText("就绪"))
                return

            # 检查历史建议
            suggestions = self.data_model.get_pattern_suggestions(
                violation.get('hier', ''), violation.get('check_info', '')
            )

            # 显示确认对话框
            dialog = ConfirmationDialog(self, violation, suggestions)
            dialog.setModal(True)  # 确保模态

            # 使用exec_()而不是show()
            result_code = dialog.exec_()

            if result_code == QDialog.Accepted:
                result = dialog.get_result()

                # 检查是否为批量处理模式
                if self.current_case_name == "回归批量处理" and isinstance(violation_id, str):
                    # 批量处理模式：直接更新内存数据，不操作数据库
                    print(f"批量处理模式：更新内存中的违例数据，ID: {violation_id}")

                    # 在内存中找到对应的违例并更新状态
                    for mem_violation in self.current_violations:
                        if str(mem_violation.get('id', '')) == str(violation_id):
                            mem_violation['status'] = 'confirmed'
                            mem_violation['confirmer'] = result['confirmer']
                            mem_violation['result'] = result['result']
                            mem_violation['reason'] = result['reason']
                            import datetime
                            mem_violation['confirmed_at'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            break

                    # 批量处理模式下单个确认也要保存历史模式
                    try:
                        self.data_model.save_pattern(
                            violation.get('hier', ''),
                            violation.get('check_info', ''),
                            result['confirmer'],
                            result['result'],
                            result['reason']
                        )
                        print(f"批量处理模式：历史模式保存成功")
                    except Exception as e:
                        print(f"批量处理模式：历史模式保存失败: {e}")

                    success = True
                else:
                    # 单文件模式：正常的数据库操作
                    update_result = self.data_model.update_confirmation(
                        violation_id,
                        status='confirmed',
                        confirmer=result['confirmer'],
                        result=result['result'],
                        reason=result['reason'],
                        is_auto=False
                    )

                    # 处理返回结果
                    if isinstance(update_result, tuple):
                        success, confirmation_data = update_result
                    else:
                        success = update_result
                        confirmation_data = None

                if success:
                    # 如果是批量模式，同时更新内存中的违例数据
                    if self.current_case_name == "回归批量处理" and hasattr(self, 'current_violations'):
                        # 在内存中找到对应的违例并更新状态
                        for mem_violation in self.current_violations:
                            if str(mem_violation.get('id', '')) == str(violation_id):
                                mem_violation['status'] = 'confirmed'
                                mem_violation['confirmer'] = result['confirmer']
                                mem_violation['result'] = result['result']
                                mem_violation['reason'] = result['reason']
                                print(f"同步更新内存中的违例数据，ID: {violation_id}")
                                break
                    
                    # 保存到历史模式
                    self.data_model.save_pattern(
                        violation.get('hier', ''),
                        violation.get('check_info', ''),
                        result['confirmer'],
                        result['result'],
                        result['reason']
                    )

                    # 使用QTimer延迟更新UI，避免死锁
                    QTimer.singleShot(0, self.safe_update_ui)

                    # 显示成功消息
                    self.status_label.setText("确认记录已更新")
                else:
                    QMessageBox.critical(self, "错误", "更新确认记录失败")

            # 确保对话框被正确销毁
            dialog.deleteLater()

        except Exception as e:
            print(f"确认违例时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"确认过程中出现错误: {str(e)}")

    def update_progress_display(self):
        """更新进度显示"""
        if not self.current_case_name:
            self.progress_label.setText("进度: 已确认 0/0 (0%)")
            self.stats_label.setText("总计: 0条违例 | 已确认: 0条 | 待确认: 0条")
            return

        # 获取统计信息 - 使用智能corner匹配
        violations = self._get_violations_smart()

        total_count = len(violations)
        confirmed_count = sum(1 for v in violations if v.get('status') in ['confirmed', 'ignored'])
        pending_count = total_count - confirmed_count

        # 计算百分比
        percentage = (confirmed_count / total_count * 100) if total_count > 0 else 0

        # 更新显示
        self.progress_label.setText(f"进度: 已确认 {confirmed_count}/{total_count} ({percentage:.1f}%)")
        self.stats_label.setText(f"总计: {total_count}条违例 | 已确认: {confirmed_count}条 | 待确认: {pending_count}条")

    def update_time_display(self):
        """更新时间显示"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(f"最后更新: {current_time}")

    def _update_violations_status_in_memory(self, violations: List[Dict], new_status: str, confirmation_data: Dict):
        """更新内存中违例的状态"""
        try:
            # 创建ID到违例的映射，用于快速查找
            violation_ids = {v.get('id') for v in violations if v.get('id')}
            
            # 更新current_violations中的状态
            if hasattr(self, 'current_violations') and self.current_violations:
                for violation in self.current_violations:
                    if violation.get('id') in violation_ids:
                        violation['status'] = new_status
                        violation['confirmer'] = confirmation_data.get('confirmer', '')
                        violation['result'] = confirmation_data.get('result', '')
                        violation['reason'] = confirmation_data.get('reason', '')
                        violation['is_auto_confirmed'] = False
                        
            print(f"已更新内存中 {len(violation_ids)} 条违例的状态为: {new_status}")
            
        except Exception as e:
            print(f"更新内存中违例状态失败: {str(e)}")

    def safe_update_ui(self):
        """安全的UI更新方法，避免死锁"""
        try:
            # 批量模式和单文件模式使用不同的刷新方法
            if self.current_case_name == "回归批量处理" and hasattr(self, 'current_violations') and self.current_violations:
                print("批量模式：安全UI更新 - 刷新违例显示")
                self.refresh_violation_display()
                
                # 强制刷新高性能表格的当前页面
                if hasattr(self, 'high_performance_table') and self.high_performance_table.isVisible():
                    print("强制刷新高性能表格当前页面")
                    self.high_performance_table.refresh_current_page()
            else:
                print("单文件模式：安全UI更新 - 更新违例表格")
                self.update_violation_table()
            
            self.update_progress_display()
        except Exception as e:
            print(f"UI更新失败: {str(e)}")
            import traceback
            traceback.print_exc()

    # 信号槽方法
    def on_violation_added(self, violation_data: Dict):
        """违例添加信号处理"""
        pass  # 在批量添加时不需要单独处理

    def on_violation_updated(self, violation_data: Dict):
        """违例更新信号处理"""
        # 使用QTimer延迟更新，避免死锁
        QTimer.singleShot(0, self.safe_update_ui)

    def on_confirmation_updated(self, confirmation_data: Dict):
        """确认更新信号处理"""
        # 使用QTimer延迟更新，避免死锁
        QTimer.singleShot(0, self.safe_update_ui)

    def on_corner_changed(self, corner: str):
        """Corner选择改变 - 更新corner标记，如果是从default切换则更新数据库"""
        print(f"Corner选择改变: '{corner}'")

        # 更新当前corner标记
        old_corner = self.current_corner
        if corner != "请选择...":
            self.current_corner = corner
        else:
            self.current_corner = ""

        print(f"Corner标记从 '{old_corner}' 变更为 '{self.current_corner}'")

        # 如果有用例数据，检查是否需要更新数据库中的corner
        if self.current_case_name and self.current_corner:
            stored_corner = self.get_stored_corner()

            # 如果当前存储的是default，且用户选择了具体corner，则更新数据库
            if stored_corner == "default" and self.current_corner != "default":
                print(f"检测到从default切换到具体corner，更新数据库...")
                success = self.data_model.update_case_corner(
                    self.current_case_name, "default", self.current_corner
                )

                if success:
                    print(f"成功更新数据库corner从 'default' 到 '{self.current_corner}'")
                    # 更新文件路径以反映新的corner
                    if self.current_file_path:
                        # 重新构造文件路径以包含corner信息
                        dir_path = os.path.dirname(os.path.dirname(self.current_file_path))
                        new_dir_path = f"{dir_path}_{self.current_corner}"
                        self.current_file_path = os.path.join(new_dir_path, "log", "vio_summary.log")
                        print(f"更新文件路径: {self.current_file_path}")
                else:
                    print("更新数据库corner失败")

        # 更新进度显示
        if self.current_case_name:
            self.update_progress_display()

        print("Corner切换完成")

    def on_case_name_changed(self, case_name: str):
        """用例名称改变"""
        self.current_case_name = case_name.strip()
        if self.current_case_name:
            self.update_violation_table()
            self.update_progress_display()

    def batch_confirm_violations(self):
        """批量确认违例 - 增强版支持大数据集和进度跟踪"""
        # 获取选中的行
        selected_rows = set()
        
        if self.use_high_performance_table:
            # 高性能表格模式下获取选中行
            if hasattr(self.high_performance_table, 'get_selected_rows'):
                selected_rows = set(self.high_performance_table.get_selected_rows())
            else:
                QMessageBox.information(
                    self, "提示",
                    "高性能模式下请使用全部确认功能，或切换到标准模式进行批量确认。"
                )
                return
        else:
            # 标准表格模式下获取选中行
            for item in self.violation_table.selectedItems():
                selected_rows.add(item.row())

        if not selected_rows:
            QMessageBox.warning(self, "警告", "请先选择要确认的违例记录")
            return

        print(f"批量确认调试：选中行数 = {len(selected_rows)}")
        print(f"批量确认调试：选中行索引 = {sorted(selected_rows)}")

        # 获取选中的违例数据 - 使用智能corner匹配
        all_violations = self._get_violations_smart()
        print(f"批量确认调试：总违例数 = {len(all_violations)}")

        # 筛选出选中的待确认违例（排除分组标识行）
        selected_violations = []
        debug_info = []

        for row in selected_rows:
            if row < len(all_violations):
                violation = all_violations[row]
                is_group_header = violation.get('is_group_header', False)
                status = violation.get('status', 'unknown')

                debug_info.append(f"行{row}: is_group_header={is_group_header}, status={status}")

                # 排除分组标识行，只处理真正的违例
                if not is_group_header and status == 'pending':
                    selected_violations.append(violation)
                    debug_info.append(f"  -> 已添加到可确认列表")
                else:
                    debug_info.append(f"  -> 跳过（分组标识行或非pending状态）")

        print("批量确认调试：选中行详情：")
        for info in debug_info:
            print(f"  {info}")

        print(f"批量确认调试：可确认违例数 = {len(selected_violations)}")

        if not selected_violations:
            QMessageBox.warning(self, "警告", "没有可确认的违例记录")
            return

        # 显示批量确认对话框
        dialog = BatchConfirmationDialog(self)
        dialog.setModal(True)
        result_code = dialog.exec_()

        if result_code == QDialog.Accepted:
            result = dialog.get_result()
            
            # 根据违例数量选择处理方式
            violation_count = len(selected_violations)
            
            if violation_count < 100:
                # 小数据集：直接处理
                self._process_small_batch_confirmation(selected_violations, result)
            else:
                # 大数据集：使用增强的批量处理
                self._process_large_batch_confirmation(selected_violations, result)

        # 确保对话框被正确销毁
        dialog.deleteLater()

    def _process_small_batch_confirmation(self, violations: List[Dict], confirmation_data: Dict):
        """处理小批量确认（<100个违例）"""
        success_count = 0
        
        for violation in violations:
            violation_id = violation.get('id')
            #print(f"批量确认处理：违例ID = {violation_id}, 状态 = {violation.get('status')}")
            if not violation_id:
                print(f"  -> 跳过：没有有效ID")
                continue
            
            # 检查是否为批量处理模式
            if self.current_case_name == "回归批量处理":
                # 批量处理模式：更新内存中的违例状态
                violation['status'] = 'confirmed'
                violation['confirmer'] = confirmation_data['confirmer']
                violation['result'] = confirmation_data['result']
                violation['reason'] = confirmation_data['reason']
                # 添加确认时间
                import datetime
                violation['confirmed_at'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # 批量处理模式下延迟保存历史模式（避免逐个保存导致性能问题）
                # 历史模式保存将在批量确认完成后统一处理

                success_count += 1
            else:
                # 单文件模式：更新数据库（如果有数据库ID）
                if isinstance(violation_id, int):
                    update_result = self.data_model.update_confirmation(
                        violation_id,
                        status='confirmed',
                        confirmer=confirmation_data['confirmer'],
                        result=confirmation_data['result'],
                        reason=confirmation_data['reason'],
                        is_auto=False
                    )

                    # 处理返回结果
                    if isinstance(update_result, tuple):
                        success, _ = update_result
                    else:
                        success = update_result

                    if success:
                        success_count += 1
                        # 保存到历史模式
                        self.data_model.save_pattern(
                            violation.get('hier', ''),
                            violation.get('check_info', ''),
                            confirmation_data['confirmer'],
                            confirmation_data['result'],
                            confirmation_data['reason']
                        )

        if success_count > 0:
            QMessageBox.information(self, "成功", f"已确认 {success_count} 条违例记录")
            
            # 批量处理模式需要刷新界面
            if self.current_case_name == "回归批量处理":
                print("批量确认完成：刷新批量处理模式界面")

                # 简化的历史模式保存（小批量确认完成后）
                if success_count > 0:
                    print("小批量确认：开始保存代表性历史模式...")
                    pattern_count = self._save_representative_patterns_fast(violations, confirmation_data)
                    print(f"小批量确认：代表性历史模式保存完成: {pattern_count} 条模式")

                self.refresh_violation_display()

                # 额外的强制刷新，确保界面更新
                QTimer.singleShot(100, lambda: self._force_refresh_after_batch_confirm())
            else:
                QTimer.singleShot(0, self.safe_update_ui)
        else:
            QMessageBox.warning(self, "警告", "没有可确认的记录")

    def _force_refresh_after_batch_confirm(self):
        """批量确认后的强制刷新"""
        try:
            print("执行批量确认后的强制刷新...")

            # 检查当前使用的表格类型并强制刷新
            if hasattr(self, 'high_performance_table') and self.high_performance_table.isVisible():
                print("强制刷新高性能表格...")

                # 重新更新数据模型
                if hasattr(self.high_performance_table, 'model') and hasattr(self.high_performance_table.model, 'update_data'):
                    self.high_performance_table.model.update_data(self.current_violations)
                    print("高性能表格数据模型已更新")

                # 刷新当前页面显示
                if hasattr(self.high_performance_table, 'refresh_current_page'):
                    self.high_performance_table.refresh_current_page()
                    print("高性能表格当前页面已刷新")
                else:
                    print("高性能表格没有refresh_current_page方法")

            elif hasattr(self, 'violation_table') and self.violation_table.isVisible():
                print("强制刷新标准表格...")

                # 重新填充标准表格
                self._use_standard_table(self.current_violations)
                print("标准表格已重新填充")

            # 更新进度显示
            self.update_progress_display()
            print("进度显示已更新")

        except Exception as e:
            print(f"强制刷新失败: {e}")
            import traceback
            traceback.print_exc()

    def _batch_save_patterns_optimized(self, violations, confirmation_data):
        """优化的批量保存历史模式方法

        Args:
            violations: 违例列表
            confirmation_data: 确认数据

        Returns:
            int: 保存的模式数量
        """
        try:
            # 根据数据量决定处理策略
            total_count = len(violations)

            if total_count <= 1000:
                # 小数据集：正常处理所有模式
                return self._save_patterns_normal(violations, confirmation_data)
            elif total_count <= 5000:
                # 中等数据集：采样处理，减少重复模式
                return self._save_patterns_sampled(violations, confirmation_data, sample_rate=0.3)
            else:
                # 大数据集：只保存代表性模式，大幅减少数据库操作
                return self._save_patterns_representative(violations, confirmation_data, max_patterns=100)

        except Exception as e:
            print(f"批量保存历史模式失败: {e}")
            return 0

    def _save_patterns_normal(self, violations, confirmation_data):
        """正常模式：保存所有唯一模式"""
        pattern_count = 0
        unique_patterns = set()

        for violation in violations:
            hier = violation.get('hier', '')
            check_info = violation.get('check_info', '')
            pattern_key = (hier, check_info)

            if pattern_key not in unique_patterns:
                unique_patterns.add(pattern_key)
                try:
                    self.data_model.save_pattern(
                        hier,
                        check_info,
                        confirmation_data['confirmer'],
                        confirmation_data['result'],
                        confirmation_data['reason']
                    )
                    pattern_count += 1
                except Exception:
                    pass

        return pattern_count

    def _save_patterns_sampled(self, violations, confirmation_data, sample_rate=0.3):
        """采样模式：按比例采样保存模式"""
        import random

        # 先去重获取唯一模式
        unique_patterns = {}
        for violation in violations:
            hier = violation.get('hier', '')
            check_info = violation.get('check_info', '')
            pattern_key = (hier, check_info)

            if pattern_key not in unique_patterns:
                unique_patterns[pattern_key] = violation

        # 按采样率选择模式
        pattern_list = list(unique_patterns.items())
        sample_size = max(1, int(len(pattern_list) * sample_rate))
        sampled_patterns = random.sample(pattern_list, min(sample_size, len(pattern_list)))

        pattern_count = 0
        for pattern_key, violation in sampled_patterns:
            try:
                self.data_model.save_pattern(
                    violation.get('hier', ''),
                    violation.get('check_info', ''),
                    confirmation_data['confirmer'],
                    confirmation_data['result'],
                    confirmation_data['reason']
                )
                pattern_count += 1
            except Exception:
                pass

        print(f"采样模式：从 {len(unique_patterns)} 个唯一模式中采样 {len(sampled_patterns)} 个")
        return pattern_count

    def _save_patterns_representative(self, violations, confirmation_data, max_patterns=100):
        """代表性模式：只保存最具代表性的模式"""
        # 统计模式频率
        pattern_frequency = {}
        for violation in violations:
            hier = violation.get('hier', '')
            check_info = violation.get('check_info', '')
            pattern_key = (hier, check_info)

            if pattern_key not in pattern_frequency:
                pattern_frequency[pattern_key] = {'count': 0, 'violation': violation}
            pattern_frequency[pattern_key]['count'] += 1

        # 按频率排序，选择最常见的模式
        sorted_patterns = sorted(
            pattern_frequency.items(),
            key=lambda x: x[1]['count'],
            reverse=True
        )

        # 只保存前max_patterns个最常见的模式
        top_patterns = sorted_patterns[:max_patterns]

        pattern_count = 0
        for pattern_key, pattern_data in top_patterns:
            violation = pattern_data['violation']
            try:
                self.data_model.save_pattern(
                    violation.get('hier', ''),
                    violation.get('check_info', ''),
                    confirmation_data['confirmer'],
                    confirmation_data['result'],
                    confirmation_data['reason']
                )
                pattern_count += 1
            except Exception:
                pass

        print(f"代表性模式：从 {len(pattern_frequency)} 个唯一模式中选择 {len(top_patterns)} 个最常见的模式")
        return pattern_count

    def _start_background_pattern_save(self, violations, confirmation_data):
        """启动后台历史模式保存"""
        try:
            # 检查是否已有后台保存线程在运行
            if hasattr(self, 'pattern_save_thread') and self.pattern_save_thread.isRunning():
                print("后台历史模式保存线程已在运行，跳过新的保存请求")
                return

            # 创建后台保存线程
            self.pattern_save_thread = PatternSaveThread(violations, confirmation_data, self.data_model)
            self.pattern_save_thread.save_completed.connect(self._on_pattern_save_completed)
            self.pattern_save_thread.save_failed.connect(self._on_pattern_save_failed)
            self.pattern_save_thread.finished.connect(self._on_pattern_save_thread_finished)
            self.pattern_save_thread.start()

            # 设置数据库忙碌标志
            self._db_busy_with_background_save = True

            print("后台历史模式保存线程已启动")

        except Exception as e:
            print(f"启动后台历史模式保存失败: {e}")

    def _on_pattern_save_completed(self, pattern_count):
        """后台历史模式保存完成"""
        print(f"后台历史模式保存完成: {pattern_count} 条唯一模式")

    def _on_pattern_save_failed(self, error_message):
        """后台历史模式保存失败"""
        print(f"后台历史模式保存失败: {error_message}")

    def _on_pattern_save_thread_finished(self):
        """后台保存线程完成"""
        self._db_busy_with_background_save = False
        print("后台历史模式保存线程已完成")

    def _save_representative_patterns_fast(self, violations, confirmation_data, max_patterns=50):
        """快速保存代表性历史模式（主线程，但限制数量避免卡死）

        Args:
            violations: 违例列表
            confirmation_data: 确认数据
            max_patterns: 最大保存模式数量，默认50个

        Returns:
            int: 保存的模式数量
        """
        try:
            # 统计模式频率
            pattern_frequency = {}
            for violation in violations:
                if violation.get('is_group_header', False):
                    continue  # 跳过分组标识行

                hier = violation.get('hier', '')
                check_info = violation.get('check_info', '')
                pattern_key = (hier, check_info)

                if pattern_key not in pattern_frequency:
                    pattern_frequency[pattern_key] = {'count': 0, 'violation': violation}
                pattern_frequency[pattern_key]['count'] += 1

            # 按频率排序，选择最常见的模式
            sorted_patterns = sorted(
                pattern_frequency.items(),
                key=lambda x: x[1]['count'],
                reverse=True
            )

            # 只保存前max_patterns个最常见的模式
            top_patterns = sorted_patterns[:max_patterns]

            pattern_count = 0
            for pattern_key, pattern_data in top_patterns:
                violation = pattern_data['violation']
                try:
                    self.data_model.save_pattern(
                        violation.get('hier', ''),
                        violation.get('check_info', ''),
                        confirmation_data['confirmer'],
                        confirmation_data['result'],
                        confirmation_data['reason']
                    )
                    pattern_count += 1
                except Exception:
                    pass

            print(f"快速代表性模式：从 {len(pattern_frequency)} 个唯一模式中选择 {len(top_patterns)} 个最常见的模式")
            return pattern_count

        except Exception as e:
            print(f"快速保存代表性历史模式失败: {e}")
            return 0

    def _process_large_batch_confirmation(self, violations: List[Dict], confirmation_data: Dict):
        """处理大批量确认（>=100个违例）"""
        # 创建批量处理器
        if not hasattr(self, 'batch_processor'):
            self.batch_processor = ViolationBatchProcessor(self.data_model, self)
        
        # 检查是否已有批量操作在进行中
        if (hasattr(self.batch_processor, 'batch_manager') and 
            hasattr(self.batch_processor.batch_manager, 'current_worker') and
            self.batch_processor.batch_manager.current_worker and 
            self.batch_processor.batch_manager.current_worker.isRunning()):
            QMessageBox.warning(self, "警告", "批量确认操作已启动，请等待当前操作完成")
            return
        
        # 创建进度对话框
        progress_dialog = BatchProgressDialog(self, "批量确认违例")
        
        # 确保批量管理器存在
        if not hasattr(self.batch_processor, 'batch_manager'):
            print("错误：批量管理器不存在")
            return
        
        # 连接信号 - 使用Qt.QueuedConnection确保线程安全
        from PyQt5.QtCore import Qt
        self.batch_processor.batch_manager.progress_updated.connect(
            progress_dialog.update_progress, Qt.QueuedConnection)
        self.batch_processor.batch_manager.operation_completed.connect(
            progress_dialog.on_operation_completed, Qt.QueuedConnection)
        self.batch_processor.batch_manager.operation_completed.connect(
            self._on_batch_confirmation_completed, Qt.QueuedConnection)
        
        # 连接取消信号
        progress_dialog.cancel_requested.connect(self.batch_processor.cancel_batch_operation)
        
        # 检查是否为批量处理模式
        if self.current_case_name == "回归批量处理":
            # 批量处理模式：直接在内存中处理，不使用数据库批量更新
            print(f"批量处理模式：直接在内存中确认 {len(violations)} 条违例")

            # 显示处理提示
            from PyQt5.QtWidgets import QProgressDialog

            processing_dialog = QProgressDialog("正在批量确认违例，请稍候...", "取消", 0, 0, self)
            processing_dialog.setWindowTitle("批量确认")
            processing_dialog.setWindowModality(Qt.WindowModal)
            processing_dialog.setMinimumDuration(0)
            processing_dialog.setValue(0)
            processing_dialog.show()

            # 处理事件以显示对话框
            QApplication.processEvents()

            # 直接在内存中更新状态
            success_count = 0
            for violation in violations:
                violation['status'] = 'confirmed'
                violation['confirmer'] = confirmation_data['confirmer']
                violation['result'] = confirmation_data['result']
                violation['reason'] = confirmation_data['reason']
                import datetime
                violation['confirmed_at'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                success_count += 1

            # 简化的历史模式保存 - 只保存代表性模式，避免阻塞GUI
            print("开始保存代表性历史模式...")
            pattern_count = self._save_representative_patterns_fast(violations, confirmation_data)
            print(f"代表性历史模式保存完成: {pattern_count} 条模式")

            processing_dialog.close()

            # 显示成功消息
            QMessageBox.information(self, "成功", f"已确认 {success_count} 条违例记录")

            # 刷新界面
            self.refresh_violation_display()
            return

        # 单文件模式：使用快速批量确认方法
        elif len(violations) > 1000:
            # 大数据集使用快速方法
            progress_dialog.close()  # 关闭进度对话框

            # 显示处理提示
            from PyQt5.QtWidgets import QProgressDialog

            processing_dialog = QProgressDialog("正在批量确认违例，请稍候...", "取消", 0, 0, self)
            processing_dialog.setWindowTitle("批量确认")
            processing_dialog.setWindowModality(Qt.WindowModal)
            processing_dialog.setMinimumDuration(0)
            processing_dialog.setValue(0)
            processing_dialog.show()

            # 处理事件以显示对话框
            QApplication.processEvents()

            success, message = self.batch_processor.batch_confirm_violations_fast(
                violations,
                confirmation_data
            )

            processing_dialog.close()

            if success:
                # 快速方法成功，需要更新内存中的违例状态
                self._update_violations_status_in_memory(violations, 'confirmed', confirmation_data)

                # 显示成功消息
                QMessageBox.information(self, "成功", message)

                # 刷新界面
                QTimer.singleShot(0, self.safe_update_ui)
                return
            else:
                # 快速方法失败，重新创建进度对话框使用标准方法
                QMessageBox.warning(self, "警告", f"快速确认失败: {message}\n将使用标准方法重试...")
                progress_dialog = BatchProgressDialog(self, "批量确认违例")
                self.batch_processor.batch_manager.progress_updated.connect(progress_dialog.update_progress)
                self.batch_processor.batch_manager.operation_completed.connect(progress_dialog.on_operation_completed)
                self.batch_processor.batch_manager.operation_completed.connect(self._on_batch_confirmation_completed)
                progress_dialog.cancel_requested.connect(self.batch_processor.cancel_batch_operation)
        
        # 使用标准批量操作（带进度显示）
        success, message = self.batch_processor.batch_confirm_violations(
            violations, 
            confirmation_data
        )
        
        if success:
            print(f"批量确认操作启动成功，处理 {len(violations)} 条违例")
            progress_dialog.exec_()
        else:
            print(f"批量确认操作启动失败：{message}")
            QMessageBox.warning(self, "错误", f"无法启动批量确认操作：{message}")

    def _on_batch_confirmation_completed(self, success: bool, message: str):
        """批量确认完成回调"""
        if success:
            # 批量处理模式需要刷新界面
            if self.current_case_name == "回归批量处理":
                print("大批量确认完成：刷新批量处理模式界面")
                self.refresh_violation_display()

                # 额外的强制刷新，确保界面更新
                QTimer.singleShot(100, lambda: self._force_refresh_after_batch_confirm())
            else:
                # 单文件模式使用标准更新
                self.update_violation_table()
                self.update_progress_display()
        
        # 显示结果消息
        if success:
            QMessageBox.information(self, "完成", message)
        else:
            QMessageBox.warning(self, "失败", message)

    def batch_update_violation_status(self, new_status: str):
        """批量更新违例状态"""
        # 获取选中的违例
        selected_violations = self._get_selected_violations()
        
        if not selected_violations:
            QMessageBox.warning(self, "警告", "请先选择要更新的违例记录")
            return
        
        # 确认操作
        reply = QMessageBox.question(
            self, "确认",
            f"确定要将选中的 {len(selected_violations)} 条违例状态更新为 '{new_status}' 吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 创建批量处理器
            if not hasattr(self, 'batch_processor'):
                self.batch_processor = ViolationBatchProcessor(self.data_model, self)
            
            violation_count = len(selected_violations)
            
            if violation_count < 100:
                # 小数据集：直接处理
                self._process_small_batch_status_update(selected_violations, new_status)
            else:
                # 大数据集：使用增强的批量处理
                self._process_large_batch_status_update(selected_violations, new_status)

    def _process_small_batch_status_update(self, violations: List[Dict], new_status: str):
        """处理小批量状态更新"""
        success_count = 0
        
        for violation in violations:
            violation_id = violation.get('id')
            if not violation_id:
                continue
            
            try:
                # 更新状态
                if hasattr(self.data_model, 'update_violation_status'):
                    success = self.data_model.update_violation_status(violation_id, new_status)
                else:
                    # 使用通用的更新方法
                    update_result = self.data_model.update_confirmation(
                        violation_id,
                        status=new_status,
                        confirmer='',
                        result='',
                        reason='批量状态更新',
                        is_auto=True
                    )
                    
                    if isinstance(update_result, tuple):
                        success, _ = update_result
                    else:
                        success = update_result
                
                if success:
                    success_count += 1
                    
            except Exception:
                continue
        
        if success_count > 0:
            QMessageBox.information(self, "成功", f"已更新 {success_count} 条违例状态")
            QTimer.singleShot(0, self.safe_update_ui)
        else:
            QMessageBox.warning(self, "警告", "没有成功更新的记录")

    def _process_large_batch_status_update(self, violations: List[Dict], new_status: str):
        """处理大批量状态更新"""
        # 创建进度对话框
        progress_dialog = BatchProgressDialog(self, f"批量更新状态为{new_status}")
        
        # 连接信号
        self.batch_processor.batch_manager.progress_updated.connect(progress_dialog.update_progress)
        self.batch_processor.batch_manager.operation_completed.connect(progress_dialog.on_operation_completed)
        self.batch_processor.batch_manager.operation_completed.connect(self._on_batch_status_update_completed)
        
        # 连接取消信号
        progress_dialog.cancel_requested.connect(self.batch_processor.cancel_batch_operation)
        
        # 启动批量操作
        success, message = self.batch_processor.batch_update_status(violations, new_status)
        
        if success:
            progress_dialog.exec_()
        else:
            QMessageBox.warning(self, "错误", f"无法启动批量状态更新操作：{message}")

    def _on_batch_status_update_completed(self, success: bool, message: str):
        """批量状态更新完成回调"""
        if success:
            # 更新UI
            QTimer.singleShot(0, self.safe_update_ui)
        
        # 显示结果消息
        if success:
            QMessageBox.information(self, "完成", message)
        else:
            QMessageBox.warning(self, "失败", message)

    def batch_export_violations(self, export_format: str = 'csv'):
        """批量导出违例数据"""
        # 获取要导出的违例 - 使用智能corner匹配
        all_violations = self._get_violations_smart()
        
        if not all_violations:
            QMessageBox.warning(self, "警告", "没有可导出的违例数据")
            return
        
        # 选择输出文件
        file_extensions = {
            'csv': 'CSV文件 (*.csv)',
            'excel': 'Excel文件 (*.xlsx)',
            'json': 'JSON文件 (*.json)'
        }
        
        file_filter = file_extensions.get(export_format, 'CSV文件 (*.csv)')
        output_file, _ = QFileDialog.getSaveFileName(
            self, f"导出违例数据 ({export_format.upper()})", 
            f"violations_export.{export_format}", 
            file_filter
        )
        
        if not output_file:
            return
        
        # 创建批量处理器
        if not hasattr(self, 'batch_processor'):
            self.batch_processor = ViolationBatchProcessor(self.data_model, self)
        
        violation_count = len(all_violations)
        
        if violation_count < 1000:
            # 小数据集：直接处理
            self._process_small_batch_export(all_violations, export_format, output_file)
        else:
            # 大数据集：使用增强的批量处理
            self._process_large_batch_export(all_violations, export_format, output_file)

    def _process_small_batch_export(self, violations: List[Dict], export_format: str, output_file: str):
        """处理小批量导出"""
        try:
            # 准备导出数据
            export_data = []
            for violation in violations:
                export_item = {
                    'ID': violation.get('id', ''),
                    'Hierarchy': violation.get('hier', ''),
                    'Check': violation.get('check_info', ''),
                    'Status': violation.get('status', ''),
                    'Slack': violation.get('slack', ''),
                    'Required': violation.get('required', ''),
                    'Actual': violation.get('actual', ''),
                    'Confirmer': violation.get('confirmer', ''),
                    'Result': violation.get('result', ''),
                    'Reason': violation.get('reason', ''),
                    'Timestamp': violation.get('timestamp', '')
                }
                export_data.append(export_item)
            
            # 写入文件
            self.batch_processor._write_export_file(export_data, output_file, export_format)
            
            QMessageBox.information(self, "成功", f"已导出 {len(export_data)} 条违例记录到 {output_file}")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败：{str(e)}")

    def _process_large_batch_export(self, violations: List[Dict], export_format: str, output_file: str):
        """处理大批量导出"""
        # 创建进度对话框
        progress_dialog = BatchProgressDialog(self, f"批量导出违例数据({export_format.upper()})")
        
        # 连接信号
        self.batch_processor.batch_manager.progress_updated.connect(progress_dialog.update_progress)
        self.batch_processor.batch_manager.operation_completed.connect(progress_dialog.on_operation_completed)
        self.batch_processor.batch_manager.operation_completed.connect(self._on_batch_export_completed)
        
        # 连接取消信号
        progress_dialog.cancel_requested.connect(self.batch_processor.cancel_batch_operation)
        
        # 启动批量操作
        success, message = self.batch_processor.batch_export_violations(
            violations, export_format, output_file
        )
        
        if success:
            progress_dialog.exec_()
        else:
            QMessageBox.warning(self, "错误", f"无法启动批量导出操作：{message}")

    def _on_batch_export_completed(self, success: bool, message: str):
        """批量导出完成回调"""
        # 显示结果消息
        if success:
            QMessageBox.information(self, "完成", "违例数据导出完成")
        else:
            QMessageBox.warning(self, "失败", f"导出失败：{message}")

    def _get_selected_violations(self) -> List[Dict]:
        """获取选中的违例列表"""
        selected_violations = []
        all_violations = self._get_violations_smart()
        
        if self.use_high_performance_table:
            # 高性能表格模式
            if hasattr(self.high_performance_table, 'get_selected_rows'):
                selected_rows = self.high_performance_table.get_selected_rows()
                for row in selected_rows:
                    if row < len(all_violations):
                        violation = all_violations[row]
                        # 排除分组标识行，只返回真正的违例
                        if not violation.get('is_group_header', False):
                            selected_violations.append(violation)
        else:
            # 标准表格模式
            selected_rows = set()
            for item in self.violation_table.selectedItems():
                selected_rows.add(item.row())
            
            for row in selected_rows:
                if row < len(all_violations):
                    violation = all_violations[row]
                    # 排除分组标识行，只返回真正的违例
                    if not violation.get('is_group_header', False):
                        selected_violations.append(violation)
        
        return selected_violations

    def confirm_all_violations(self):
        """确认所有违例 - 增强版支持大数据集和进度跟踪"""
        if not self.current_case_name:
            QMessageBox.warning(self, "警告", "请先选择日志文件")
            return

        # 获取待确认的违例数量 - 使用智能corner匹配，排除分组标识行
        violations = self._get_violations_smart()
        pending_violations = [v for v in violations if not v.get('is_group_header', False) and v.get('status') == 'pending']

        if not pending_violations:
            QMessageBox.information(self, "提示", "没有待确认的违例记录")
            return

        # 确认操作
        reply = QMessageBox.question(
            self, "确认",
            f"确定要确认所有 {len(pending_violations)} 条待确认的违例记录吗？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 显示批量确认对话框
            dialog = BatchConfirmationDialog(self)
            dialog.setModal(True)
            result_code = dialog.exec_()

            if result_code == QDialog.Accepted:
                result = dialog.get_result()
                violation_count = len(pending_violations)
                
                if violation_count < 100:
                    # 小数据集：直接处理
                    self._process_small_batch_confirmation(pending_violations, result)
                else:
                    # 大数据集：使用增强的批量处理
                    self._process_large_batch_confirmation(pending_violations, result)

            # 确保对话框被正确销毁
            dialog.deleteLater()

    def edit_confirmation(self, violation_id):
        """编辑确认记录（支持字符串和整数ID）"""
        if not violation_id:
            return

        try:
            # 获取违例信息 - 使用智能corner匹配
            violations = self._get_violations_smart()
            
            # 支持字符串ID（批量处理）和整数ID（单文件处理）
            if isinstance(violation_id, str):
                violation = next((v for v in violations if str(v.get('id', '')) == violation_id), None)
            else:
                violation = next((v for v in violations if v.get('id') == violation_id), None)

            if not violation:
                # 静默处理，避免弹窗
                print(f"编辑时找不到违例记录，ID: {violation_id}")
                if hasattr(self, 'status_label'):
                    self.status_label.setText("找不到违例记录")
                    QTimer.singleShot(3000, lambda: self.status_label.setText("就绪"))
                return

            # 显示编辑对话框
            dialog = ConfirmationDialog(self, violation, None, edit_mode=True)
            dialog.setModal(True)  # 确保模态

            result_code = dialog.exec_()

            if result_code == QDialog.Accepted:
                result = dialog.get_result()

                # 更新确认记录
                update_result = self.data_model.update_confirmation(
                    violation_id,
                    status='confirmed',
                    confirmer=result['confirmer'],
                    result=result['result'],
                    reason=result['reason'],
                    is_auto=False
                )

                # 处理返回结果
                if isinstance(update_result, tuple):
                    success, _ = update_result
                else:
                    success = update_result

                if success:
                    # 如果是批量处理模式，同时更新内存中的违例状态
                    if self.current_case_name == "回归批量处理":
                        # 更新内存中的违例状态
                        violation['status'] = 'confirmed'
                        violation['confirmer'] = result['confirmer']
                        violation['result'] = result['result']
                        violation['reason'] = result['reason']
                        # 添加确认时间
                        import datetime
                        violation['confirmed_at'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        print(f"编辑确认：更新内存中违例状态 - ID: {violation_id}")
                    
                    # 更新历史模式
                    self.data_model.save_pattern(
                        violation.get('hier', ''),
                        violation.get('check_info', ''),
                        result['confirmer'],
                        result['result'],
                        result['reason']
                    )

                    # 使用QTimer延迟更新UI，避免死锁
                    QTimer.singleShot(0, self.safe_update_ui)

                    # 显示成功消息
                    self.status_label.setText("确认记录已更新")
                else:
                    QMessageBox.critical(self, "错误", "更新确认记录失败")

            # 确保对话框被正确销毁
            dialog.deleteLater()

        except Exception as e:
            print(f"编辑确认时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"编辑过程中出现错误: {str(e)}")

    def refresh_data(self):
        """刷新数据"""
        # 检查是否为批量处理模式
        if self.current_case_name == "回归批量处理":
            print("批量处理模式：刷新违例显示")
            self.refresh_violation_display()
        elif self.current_file_path and os.path.isfile(self.current_file_path):
            # 单文件模式且文件存在
            self.load_log_file(self.current_file_path)
        else:
            # 其他情况直接更新表格
            self.update_violation_table()
            self.update_progress_display()



    def export_to_excel(self):
        """导出为Excel文件"""
        if not self.current_case_name:
            QMessageBox.warning(self, "警告", "没有数据可导出")
            return

        # 检查表格中是否有数据 - 支持高性能模式和标准模式
        if self.use_high_performance_table:
            # 高性能模式：检查高性能表格的数据
            table_row_count = self.high_performance_table.model.rowCount()
            print(f"导出Excel - 高性能模式，表格中有 {table_row_count} 行数据")
        else:
            # 标准模式：检查标准表格的数据
            table_row_count = self.violation_table.rowCount()
            print(f"导出Excel - 标准模式，表格中有 {table_row_count} 行数据")

        if table_row_count == 0:
            QMessageBox.warning(self, "警告", "表格中没有数据可导出，请先加载违例数据")
            return

        # 检查是否为批量处理模式
        if self.current_case_name == "回归批量处理" and hasattr(self, 'current_violations') and self.current_violations:
            self._export_batch_violations_to_excel()
        else:
            self._export_single_case_to_excel()

    def _export_single_case_to_excel(self):
        """导出单个case的Excel文件"""
        # 构建默认保存路径
        corner_name = self.get_display_corner()
        default_dir = os.path.join(os.getcwd(), "VIOLATION_CHECK", corner_name)

        # 确保目录存在
        os.makedirs(default_dir, exist_ok=True)

        default_name = f"{self.current_case_name}_{corner_name}_violations_checklist.xlsx"
        default_path = os.path.join(default_dir, default_name)

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出Excel文件", default_path, "Excel文件 (*.xlsx)"
        )

        if file_path:
            try:
                self._export_data_to_excel(file_path)
                QMessageBox.information(self, "成功", f"数据已导出到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")

    def _export_batch_violations_to_excel(self):
        """导出批量处理模式下的违例数据到Excel文件（按case分别导出）"""
        try:
            # 按case和corner分组违例数据
            violations_by_case = {}
            for violation in self.current_violations:
                # 使用违例数据中已经存储的来源信息
                case_name = violation.get('source_case', 'unknown')
                corner_name = violation.get('source_corner', 'unknown')
                seed = violation.get('source_seed', 'unknown')
                
                if case_name and corner_name and case_name != 'unknown':
                    key = (case_name, corner_name, seed)
                    if key not in violations_by_case:
                        violations_by_case[key] = []
                    violations_by_case[key].append(violation)

            if not violations_by_case:
                QMessageBox.warning(self, "警告", "无法从批量数据中提取case信息")
                return

            # 为每个case创建Excel文件
            exported_files = []
            for (case_name, corner_name, seed), case_violations in violations_by_case.items():
                # 创建目录结构：{corner_name}
                export_dir = os.path.join(os.getcwd(), "VIOLATION_CHECK", corner_name)
                os.makedirs(export_dir, exist_ok=True)
                
                # 生成文件名：regr_{case_name}_{corner_name}_{seed}_violations_checklist.xlsx
                filename = f"regr_{case_name}_{corner_name}_{seed}_violations_checklist.xlsx"
                file_path = os.path.join(export_dir, filename)
                
                # 导出该case的数据
                self._export_case_data_to_excel(file_path, case_violations, case_name, corner_name, seed)
                exported_files.append(file_path)
                print(f"已导出: {file_path}")

            # 显示导出结果
            if exported_files:
                message = f"批量导出完成！共导出 {len(exported_files)} 个Excel文件：\n\n"
                for file_path in exported_files[:5]:  # 最多显示5个文件路径
                    message += f"• {os.path.basename(file_path)}\n"
                if len(exported_files) > 5:
                    message += f"• ... 还有 {len(exported_files) - 5} 个文件\n"
                message += f"\n导出目录: {os.path.dirname(exported_files[0])}"
                QMessageBox.information(self, "导出成功", message)
            else:
                QMessageBox.warning(self, "警告", "没有成功导出任何文件")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"批量导出失败: {str(e)}")

    def _extract_case_info_from_path(self, file_path: str):
        """从文件路径中提取case、corner和seed信息"""
        try:
            # 文件路径格式通常为: .../case_name/corner_name/seed_xxx/vio_summary.log
            # 或者其他类似格式
            path_parts = file_path.replace('\\', '/').split('/')
            
            # 寻找包含seed信息的部分
            seed = "unknown"
            case_name = "unknown"
            corner_name = "unknown"
            
            for i, part in enumerate(path_parts):
                if part.startswith('seed_'):
                    seed = part.replace('seed_', '')
                    # case通常在seed的前两级目录
                    if i >= 2:
                        corner_name = path_parts[i-1]
                        case_name = path_parts[i-2]
                    elif i >= 1:
                        corner_name = path_parts[i-1]
                        case_name = corner_name  # 如果只有一级，case和corner相同
                    break
            
            # 如果没有找到seed信息，尝试其他方式提取
            if seed == "unknown":
                # 从文件名或路径中提取更多信息
                for part in reversed(path_parts):
                    if part and part != "vio_summary.log":
                        if corner_name == "unknown":
                            corner_name = part
                        elif case_name == "unknown":
                            case_name = part
                        else:
                            break
            
            return case_name, corner_name, seed
            
        except Exception as e:
            print(f"提取case信息失败: {e}")
            return "unknown", "unknown", "unknown"

    def _export_case_data_to_excel(self, file_path: str, violations: list, case_name: str, corner_name: str, seed: str):
        """导出单个case的数据到Excel文件"""
        try:
            import openpyxl
            from openpyxl.styles import Font, Alignment, PatternFill
        except ImportError:
            raise ImportError("需要安装openpyxl库: pip install openpyxl")

        print(f"导出Excel - Case: {case_name}, Corner: {corner_name}, Seed: {seed}")
        print(f"导出Excel - 违例数量: {len(violations)}")

        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "时序违例确认清单"

        # 设置表头
        headers = ["序号", "层级路径", "时间(ns)", "检查信息", "状态", "确认人", "确认结果", "确认理由", "确认时间"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        # 填充数据
        for row, violation in enumerate(violations, 2):
            # 兼容两种数据格式：数据库格式(小写)和解析器格式(大写)
            num = violation.get('num', violation.get('NUM', ''))
            hier = violation.get('hier', violation.get('Hier', ''))
            time_fs = violation.get('time_fs', violation.get('time_fs', 0))
            check_info = violation.get('check_info', violation.get('Check', ''))
            status = violation.get('status', '')
            confirmer = violation.get('confirmer', '')
            result = violation.get('result', '')
            reason = violation.get('reason', '')
            confirmed_at = violation.get('confirmed_at', '')

            ws.cell(row=row, column=1, value=num)
            ws.cell(row=row, column=2, value=hier)
            ws.cell(row=row, column=3, value=time_fs / 1000000 if time_fs else 0)
            ws.cell(row=row, column=4, value=check_info)
            ws.cell(row=row, column=5, value=self.get_status_display(status))
            ws.cell(row=row, column=6, value=confirmer)
            ws.cell(row=row, column=7, value=self.get_result_display(result))
            ws.cell(row=row, column=8, value=reason)
            ws.cell(row=row, column=9, value=confirmed_at)

        print(f"导出Excel - 完成数据填充，共处理 {len(violations)} 条记录")

        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 保存文件
        wb.save(file_path)

    def export_to_csv(self):
        """导出为CSV文件"""
        if not self.current_case_name:
            QMessageBox.warning(self, "警告", "没有数据可导出")
            return

        # 检查表格中是否有数据 - 支持高性能模式和标准模式
        if self.use_high_performance_table:
            # 高性能模式：检查高性能表格的数据
            table_row_count = self.high_performance_table.model.rowCount()
            print(f"导出CSV - 高性能模式，表格中有 {table_row_count} 行数据")
        else:
            # 标准模式：检查标准表格的数据
            table_row_count = self.violation_table.rowCount()
            print(f"导出CSV - 标准模式，表格中有 {table_row_count} 行数据")

        if table_row_count == 0:
            QMessageBox.warning(self, "警告", "表格中没有数据可导出，请先加载违例数据")
            return

        # 检查是否为批量处理模式
        if self.current_case_name == "回归批量处理" and hasattr(self, 'current_violations') and self.current_violations:
            self._export_batch_violations_to_csv()
        else:
            self._export_single_case_to_csv()

    def _export_single_case_to_csv(self):
        """导出单个case的CSV文件"""
        # 构建默认保存路径
        corner_name = self.get_display_corner()
        default_dir = os.path.join(os.getcwd(), "VIOLATION_CHECK", corner_name)

        # 确保目录存在
        os.makedirs(default_dir, exist_ok=True)

        default_name = f"{self.current_case_name}_{corner_name}_violations_checklist.csv"
        default_path = os.path.join(default_dir, default_name)

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出CSV文件", default_path, "CSV文件 (*.csv)"
        )

        if file_path:
            try:
                self._export_data_to_csv(file_path)
                QMessageBox.information(self, "成功", f"数据已导出到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")

    def _export_batch_violations_to_csv(self):
        """导出批量处理模式下的违例数据到CSV文件（按case分别导出）"""
        try:
            # 按case和corner分组违例数据
            violations_by_case = {}
            for violation in self.current_violations:
                # 使用违例数据中已经存储的来源信息
                case_name = violation.get('source_case', 'unknown')
                corner_name = violation.get('source_corner', 'unknown')
                seed = violation.get('source_seed', 'unknown')
                
                if case_name and corner_name and case_name != 'unknown':
                    key = (case_name, corner_name, seed)
                    if key not in violations_by_case:
                        violations_by_case[key] = []
                    violations_by_case[key].append(violation)

            if not violations_by_case:
                QMessageBox.warning(self, "警告", "无法从批量数据中提取case信息")
                return

            # 为每个case创建CSV文件
            exported_files = []
            for (case_name, corner_name, seed), case_violations in violations_by_case.items():
                # 创建目录结构：{corner_name}
                export_dir = os.path.join(os.getcwd(), "VIOLATION_CHECK", corner_name)
                os.makedirs(export_dir, exist_ok=True)
                
                # 生成文件名：regr_{case_name}_{corner_name}_{seed}_violations_checklist.csv
                filename = f"regr_{case_name}_{corner_name}_{seed}_violations_checklist.csv"
                file_path = os.path.join(export_dir, filename)
                
                # 导出该case的数据
                self._export_case_data_to_csv(file_path, case_violations, case_name, corner_name, seed)
                exported_files.append(file_path)
                print(f"已导出: {file_path}")

            # 显示导出结果
            if exported_files:
                message = f"批量导出完成！共导出 {len(exported_files)} 个CSV文件：\n\n"
                for file_path in exported_files[:5]:  # 最多显示5个文件路径
                    message += f"• {os.path.basename(file_path)}\n"
                if len(exported_files) > 5:
                    message += f"• ... 还有 {len(exported_files) - 5} 个文件\n"
                message += f"\n导出目录: {os.path.dirname(exported_files[0])}"
                QMessageBox.information(self, "导出成功", message)
            else:
                QMessageBox.warning(self, "警告", "没有成功导出任何文件")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"批量导出失败: {str(e)}")

    def _export_case_data_to_csv(self, file_path: str, violations: list, case_name: str, corner_name: str, seed: str):
        """导出单个case的数据到CSV文件"""
        import csv

        print(f"导出CSV - Case: {case_name}, Corner: {corner_name}, Seed: {seed}")
        print(f"导出CSV - 违例数量: {len(violations)}")

        # 写入CSV文件
        with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)

            # 写入表头
            headers = ["序号", "层级路径", "时间(ns)", "检查信息", "状态", "确认人", "确认结果", "确认理由", "确认时间"]
            writer.writerow(headers)

            # 写入数据
            for i, violation in enumerate(violations):
                # 兼容两种数据格式：数据库格式(小写)和解析器格式(大写)
                num = violation.get('num', violation.get('NUM', ''))
                hier = violation.get('hier', violation.get('Hier', ''))
                time_fs = violation.get('time_fs', violation.get('time_fs', 0))
                check_info = violation.get('check_info', violation.get('Check', ''))
                status = violation.get('status', '')
                confirmer = violation.get('confirmer', '')
                result = violation.get('result', '')
                reason = violation.get('reason', '')
                confirmed_at = violation.get('confirmed_at', '')

                row = [
                    num,
                    hier,
                    time_fs / 1000000 if time_fs else 0,
                    check_info,
                    self.get_status_display(status),
                    confirmer,
                    self.get_result_display(result),
                    reason,
                    confirmed_at
                ]
                writer.writerow(row)

            print(f"导出CSV - 完成数据写入，共处理 {len(violations)} 条记录")

    def _auto_apply_historical_confirmations_for_batch(self):
        """自动应用历史确认记录（批量处理模式）"""
        try:
            if not self.current_violations:
                return

            print("批量处理模式：开始自动应用历史确认记录")

            # 按来源分组处理违例数据
            violations_by_source = {}
            for violation in self.current_violations:
                # 跳过分组标识行
                if violation.get('is_group_header', False):
                    continue
                    
                source_case = violation.get('source_case', '')
                source_corner = violation.get('source_corner', '')
                source_key = f"{source_case}_{source_corner}"
                
                if source_key not in violations_by_source:
                    violations_by_source[source_key] = []
                violations_by_source[source_key].append(violation)

            total_applied = 0
            
            # 对每个来源的违例分别应用历史确认
            for source_key, source_violations in violations_by_source.items():
                print(f"正在处理来源: {source_key}, 违例数: {len(source_violations)}")
                
                applied_count = 0
                for violation in source_violations:
                    try:
                        # 获取违例的层级和检查信息
                        hier = violation.get('hier', '')
                        check_info = violation.get('check_info', '')

                        if not hier or not check_info:
                            continue

                        # 查找匹配的历史模式
                        pattern = self.data_model._find_matching_pattern(hier, check_info)

                        if pattern:
                            confirmer, result, reason = pattern

                            # 直接更新内存中的违例状态
                            violation['status'] = 'confirmed'
                            violation['confirmer'] = confirmer
                            violation['result'] = result
                            violation['confirm_result'] = result  # 兼容字段
                            violation['confirmed_by'] = confirmer  # 兼容字段
                            violation['reason'] = reason

                            applied_count += 1
                            total_applied += 1

                    except Exception as e:
                        print(f"批量模式应用历史确认失败: {e}")
                
                print(f"来源 {source_key} 应用了 {applied_count} 条历史确认记录")

            if total_applied > 0:
                print(f"批量处理模式：总共自动应用了 {total_applied} 条历史确认记录")
                
                # 将更新后的违例数据保存到数据库
                self._save_batch_violations_to_database(self.current_violations)
                
                # 刷新界面显示
                self.refresh_violation_display()
                
                # 显示应用结果
                QMessageBox.information(
                    self, "自动应用历史确认",
                    f"已自动应用 {total_applied} 条历史确认记录"
                )
            else:
                print("批量处理模式：没有找到可应用的历史确认记录")
                QMessageBox.information(
                    self, "提示",
                    "没有找到可应用的历史确认记录"
                )
                
        except Exception as e:
            print(f"自动应用历史确认失败: {e}")
            QMessageBox.critical(self, "错误", f"应用历史确认失败: {str(e)}")

    def _sync_violations_from_database(self):
        """从数据库同步违例数据"""
        try:
            # 按来源分组获取数据库中的违例数据
            violations_by_source = {}
            for violation in self.current_violations:
                source_case = violation.get('source_case')
                source_corner = violation.get('source_corner')
                if source_case and source_corner:
                    key = (source_case, source_corner)
                    if key not in violations_by_source:
                        violations_by_source[key] = []
                    violations_by_source[key].append(violation)
            
            # 从数据库获取最新数据并更新内存中的违例
            for (case_name, corner_name), case_violations in violations_by_source.items():
                try:
                    db_violations = self.data_model.get_violations_by_case(case_name, corner_name)
                    
                    # 创建数据库违例的映射（基于hier和check_info）
                    db_violation_map = {}
                    for db_violation in db_violations:
                        key = (db_violation.get('hier', ''), db_violation.get('check_info', ''))
                        db_violation_map[key] = db_violation
                    
                    # 更新内存中的违例数据
                    for violation in case_violations:
                        key = (violation.get('hier', ''), violation.get('check_info', ''))
                        if key in db_violation_map:
                            db_data = db_violation_map[key]
                            # 更新确认信息
                            violation['id'] = db_data.get('id')
                            violation['status'] = db_data.get('status', 'pending')
                            violation['confirmer'] = db_data.get('confirmer', '')
                            violation['result'] = db_data.get('result', '')
                            violation['reason'] = db_data.get('reason', '')
                            violation['confirmed_at'] = db_data.get('confirmed_at', '')
                            
                except Exception as e:
                    print(f"同步 {case_name}_{corner_name} 数据失败: {e}")
                    
        except Exception as e:
            print(f"从数据库同步违例数据失败: {e}")



    def apply_historical_confirmations(self):
        """手动应用历史确认记录（支持批量模式）"""
        if not self.current_case_name:
            QMessageBox.warning(self, "警告", "请先选择日志文件")
            return

        try:
            # 检查是否为批量处理模式
            if self.current_case_name == "回归批量处理":
                # 批量处理模式：对每个来源分别应用历史确认
                if not self.current_violations:
                    QMessageBox.warning(self, "警告", "没有违例数据")
                    return
                
                # 按来源分组处理
                violations_by_source = {}
                for violation in self.current_violations:
                    # 跳过分组标识行
                    if violation.get('is_group_header', False):
                        continue
                        
                    source_case = violation.get('source_case', '')
                    source_corner = violation.get('source_corner', '')
                    source_key = f"{source_case}_{source_corner}"
                    
                    if source_key not in violations_by_source:
                        violations_by_source[source_key] = []
                    violations_by_source[source_key].append(violation)
                
                total_applied = 0
                for source_key, source_violations in violations_by_source.items():
                    case_name, corner_name = source_key.split('_', 1)
                    
                    # 对每个来源应用历史确认
                    applied_count = self.data_model.apply_historical_confirmations(
                        case_name, corner_name
                    )
                    total_applied += applied_count
                    print(f"来源 {source_key} 应用了 {applied_count} 条历史确认记录")
                
                if total_applied > 0:
                    QMessageBox.information(
                        self, "成功",
                        f"已自动应用 {total_applied} 条历史确认记录"
                    )
                    # 刷新界面显示
                    QTimer.singleShot(0, self.safe_update_ui)
                else:
                    QMessageBox.information(
                        self, "提示",
                        "没有找到可应用的历史确认记录"
                    )
            else:
                # 单文件模式：使用corner无关模式应用历史确认
                applied_count = self.data_model.apply_historical_confirmations(
                    self.current_case_name, None  # 传入None实现corner无关
                )

                if applied_count > 0:
                    QMessageBox.information(
                        self, "成功",
                        f"已自动应用 {applied_count} 条历史确认记录"
                    )
                    # 使用QTimer延迟更新UI，避免死锁
                    QTimer.singleShot(0, self.safe_update_ui)
                else:
                    QMessageBox.information(
                        self, "提示",
                        "没有找到可应用的历史确认记录"
                    )
        except Exception as e:
            QMessageBox.critical(self, "错误", f"应用历史确认失败: {str(e)}")

    def show_regression_batch_dialog(self):
        """显示回归批量扫描对话框"""
        try:
            dialog = RegressionBatchDialog(self)
            result = dialog.exec_()
            
            if result == QDialog.Accepted:
                selected_files = dialog.get_selected_files()
                if selected_files:
                    self.start_batch_processing(selected_files)
                else:
                    QMessageBox.warning(self, "警告", "没有选择任何文件")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开回归批量扫描对话框失败: {str(e)}")

    def start_batch_processing(self, selected_files: List[RegressionFileInfo]):
        """启动批量处理"""
        try:
            # 设置批量处理模式
            self.current_case_name = "回归批量处理"
            self.current_corner = "多个工艺角"
            self.current_file_path = f"批量处理 ({len(selected_files)} 个文件)"
            
            # 更新界面显示
            self.case_name_edit.setText(self.current_case_name)
            self.corner_combo.setCurrentText(self.current_corner)
            self.file_path_edit.setText(self.current_file_path)

            # 创建批量处理进度对话框
            progress_dialog = SimpleBatchProgressDialog(
                self,
                f"批量处理 {len(selected_files)} 个文件"
            )
            progress_dialog.update_progress(0, "正在解析文件...")
            progress_dialog.show()

            # 使用自定义的批量文件处理
            self.process_regression_files_sequentially(selected_files, progress_dialog)

        except Exception as e:
            QMessageBox.critical(
                self, "错误",
                f"启动批量处理失败：\n{str(e)}"
            )

    def clear_history(self):
        """清除历史数据"""
        if not self.current_case_name:
            QMessageBox.warning(self, "警告", "没有数据可清除")
            return

        reply = QMessageBox.question(
            self, "确认",
            f"确定要清除用例 '{self.current_case_name}' 的所有历史数据吗？\n此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                corner = self.current_corner if self.current_corner else "default"
                self.data_model.clear_case_data(self.current_case_name, corner)
                self.update_violation_table()
                self.update_progress_display()
                QMessageBox.information(self, "成功", "历史数据已清除")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"清除数据失败: {str(e)}")

    def show_history_management(self):
        """显示历史管理对话框"""
        try:
            dialog = HistoryManagementDialog(self, self.data_model)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开历史管理失败: {str(e)}")

    def show_database_merge(self):
        """显示数据库合并对话框"""
        try:
            dialog = DatabaseMergeDialog(self.data_model, self)
            if dialog.exec_() == QDialog.Accepted:
                # 合并完成后刷新数据显示
                self.refresh_data()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开数据库合并失败: {str(e)}")

    def open_web_display(self):
        """启动实时网页展示"""
        try:
            # 检查是否有数据（放宽限制，允许从数据库加载）
            if not hasattr(self, 'current_case_name') or not self.current_case_name:
                # 尝试从数据库获取数据
                if not self._has_database_data():
                    QMessageBox.warning(self, "提示", "请先选择并加载时序违例日志文件，或确保数据库中有数据")
                    return
            
            # 显示进度提示
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度
            self.progress_label.setText("正在启动网页展示...")
            QApplication.processEvents()
            
            try:
                # 获取或创建网页展示集成管理器
                from .web_display_integration import get_web_display_integration
                
                if not hasattr(self, 'web_integration') or not self.web_integration:
                    self.web_integration = get_web_display_integration(self)
                    
                    # 连接信号
                    self.web_integration.server_status_changed.connect(self.on_web_server_status_changed)
                    self.web_integration.data_update_status.connect(self.on_web_data_update_status)
                
                # 启动网页展示
                success = self.web_integration.start_web_display(auto_open_browser=True)
                
                if success:
                    # 更新按钮状态
                    self.web_display_btn.setText("刷新网页")
                    self.web_display_btn.setToolTip("刷新网页数据或重新打开浏览器")
                    
                    # 添加停止按钮（如果还没有）
                    if not hasattr(self, 'stop_web_btn'):
                        self.stop_web_btn = QPushButton("停止网页服务")
                        self.stop_web_btn.setToolTip("停止网页服务器")
                        self.stop_web_btn.clicked.connect(self.stop_web_display)
                        
                        # 将停止按钮添加到工具栏
                        toolbar_layout = self.web_display_btn.parent().layout()
                        if toolbar_layout:
                            toolbar_layout.addWidget(self.stop_web_btn)
                else:
                    QMessageBox.warning(self, "错误", "启动网页展示失败，请查看错误信息")
                    
            except ImportError as import_error:
                QMessageBox.critical(
                    self, 
                    "模块错误", 
                    f"无法导入网页显示模块：{str(import_error)}\n\n"
                    f"请确保 web_display_integration 模块已正确安装。"
                )
            except Exception as export_error:
                QMessageBox.critical(
                    self, 
                    "启动错误", 
                    f"启动网页展示时发生错误：{str(export_error)}\n\n"
                    f"请检查数据完整性和文件权限。"
                )
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开网页显示失败: {str(e)}")
        finally:
            # 隐藏进度条
            self.progress_bar.setVisible(False)
            self.progress_label.setText("进度: 已确认 0/0 (0%)")
            QApplication.processEvents()
    
    def handle_web_display_click(self):
        """处理网页显示按钮点击事件"""
        try:
            # 检查按钮当前状态
            if self.web_display_btn.text() == "网页显示":
                self.start_web_display()
            else:
                self.refresh_web_display()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"处理网页显示请求失败: {str(e)}")
    
    def start_web_display(self):
        """启动网页展示功能"""
        try:
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度
            self.progress_label.setText("正在生成网页数据...")
            QApplication.processEvents()
            
            # 使用简化的方式直接调用数据生成和Web服务器启动
            success = self.launch_web_display_simple()
            
            if success:
                # 更新按钮状态
                self.web_display_btn.setText("刷新网页")
                self.web_display_btn.setToolTip("刷新网页数据或重新打开浏览器")
                
                # 添加停止按钮（如果还没有）
                if not hasattr(self, 'stop_web_btn'):
                    self.stop_web_btn = QPushButton("停止网页服务")
                    self.stop_web_btn.setToolTip("停止网页服务器")
                    self.stop_web_btn.clicked.connect(self.stop_web_display)
                    
                    # 将停止按钮添加到工具栏
                    toolbar_layout = self.web_display_btn.parent().layout()
                    if toolbar_layout:
                        toolbar_layout.addWidget(self.stop_web_btn)
                
                self.status_bar.showMessage("网页展示已启动", 3000)
            else:
                QMessageBox.warning(self, "错误", "启动网页展示失败，请查看错误信息")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动网页展示失败: {str(e)}")
        finally:
            # 隐藏进度条
            self.progress_bar.setVisible(False)
            self.progress_label.setText("进度: 已确认 0/0 (0%)")
            QApplication.processEvents()
    
    def launch_web_display_simple(self):
        """简化的网页展示启动方法"""
        try:
            # 更新进度
            self.progress_label.setText("正在准备数据...")
            QApplication.processEvents()
            
            # 获取当前违例数据
            violations_data = self.get_all_violations()
            
            # 使用GUI专用的启动器（修复版）
            try:
                from .gui_web_launcher_fixed import launch_web_display_for_gui
            except ImportError:
                # 如果修复版不可用，尝试原版
                from .gui_web_launcher import launch_web_display_for_gui
            
            self.progress_label.setText("正在启动Web展示...")
            QApplication.processEvents()
            
            # 启动Web展示
            process, message = launch_web_display_for_gui(
                violations_data=violations_data,
                port=8000,
                auto_open_browser=True
            )
            
            if process:
                # 保存进程引用以便后续停止
                self.web_server_process = process
                
                QMessageBox.information(self, "成功", 
                                      f"网页展示已启动！\n\n"
                                      f"{message}\n\n"
                                      f"包含 {len(violations_data)} 条违例数据")
                return True
            else:
                QMessageBox.critical(self, "启动失败", message)
                return False
                
        except ImportError as e:
            # 尝试使用简单的备用方案
            try:
                return self._launch_web_display_fallback(violations_data)
            except Exception as fallback_error:
                QMessageBox.critical(self, "模块错误", 
                                   f"无法导入Web启动器模块: {str(e)}\n"
                                   f"备用方案也失败: {str(fallback_error)}\n\n"
                                   f"请检查web_display模块是否正确安装。")
                return False
        except Exception as e:
            QMessageBox.critical(self, "异常", f"启动过程中发生异常: {str(e)}")
            return False
    
    def _launch_web_display_fallback(self, violations_data):
        """Web展示启动的备用方案"""
        try:
            import json
            from datetime import datetime
            from pathlib import Path
            
            self.progress_label.setText("使用备用方案生成数据...")
            QApplication.processEvents()
            
            # 确保目录存在
            web_dir = Path("VIOLATION_CHECK/web_display")
            data_dir = web_dir / "data"
            data_dir.mkdir(parents=True, exist_ok=True)
            
            # 处理违例数据
            if not violations_data:
                violations_data = []
            
            # 提取corners和cases
            corners = set()
            cases = set()
            for violation in violations_data:
                if violation.get('corner'):
                    corners.add(violation['corner'])
                if violation.get('case') or violation.get('case_name'):
                    case_name = violation.get('case') or violation.get('case_name')
                    cases.add(case_name)
            
            corners = sorted(list(corners))
            cases = sorted(list(cases))
            
            # 生成violations.json
            violations_data_export = {
                'metadata': {
                    'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'total_violations': len(violations_data),
                    'total_corners': len(corners),
                    'total_cases': len(cases),
                    'data_sources': ['gui_fallback']
                },
                'corners': corners,
                'cases': cases,
                'violations': violations_data
            }
            
            with open(data_dir / 'violations.json', 'w', encoding='utf-8') as f:
                json.dump(violations_data_export, f, ensure_ascii=False, indent=2)
            
            # 生成index.json
            confirmed_count = len([v for v in violations_data if v.get('status', '').lower() == 'confirmed'])
            index_data = {
                'metadata': violations_data_export['metadata'],
                'corners': corners,
                'cases': cases,
                'summary': {
                    'total_violations': len(violations_data),
                    'confirmed_violations': confirmed_count,
                    'pending_violations': len(violations_data) - confirmed_count,
                    'confirmation_rate': (confirmed_count / len(violations_data) * 100) if violations_data else 0
                }
            }
            
            with open(data_dir / 'index.json', 'w', encoding='utf-8') as f:
                json.dump(index_data, f, ensure_ascii=False, indent=2)
            
            # 复制或创建HTML文件
            self._ensure_web_html_files(web_dir)
            
            # 尝试启动简单的Web服务器
            try:
                import subprocess
                import webbrowser
                import time
                
                # 查找Web服务器脚本
                server_script = Path("plugins/user/timing_violation/start_web_server.py")
                if server_script.exists():
                    # 启动Web服务器
                    self.web_server_process = subprocess.Popen([
                        sys.executable, str(server_script),
                        "--port", "8010", "--no-browser"
                    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    
                    # 等待服务器启动
                    time.sleep(2)
                    
                    if self.web_server_process.poll() is None:
                        # 服务器启动成功，打开浏览器
                        webbrowser.open('http://localhost:8010/standalone_test.html')
                        
                        QMessageBox.information(self, "成功", 
                                              f"网页展示已启动！\n\n"
                                              f"包含 {len(violations_data)} 条违例数据\n"
                                              f"访问地址: http://localhost:8010/standalone_test.html\n\n"
                                              f"注意: 使用备用Web服务器。")
                        return True
                
                # 如果Web服务器启动失败，打开CORS友好的HTML文件
                cors_friendly_file = web_dir / "cors_friendly.html"
                if cors_friendly_file.exists():
                    import os
                    os.startfile(str(cors_friendly_file))
                    
                    QMessageBox.information(self, "提示", 
                                          f"数据已生成，但Web服务器启动失败。\n\n"
                                          f"已打开CORS友好页面，其中包含解决方案。\n\n"
                                          f"建议：在命令行执行以下命令启动服务器：\n"
                                          f"python plugins/user/timing_violation/start_web_server.py")
                else:
                    QMessageBox.warning(self, "启动失败", 
                                       f"无法启动Web服务器。\n\n"
                                       f"数据已生成到: {web_dir}\n\n"
                                       f"请手动执行以下命令启动服务器：\n"
                                       f"python plugins/user/timing_violation/start_web_server.py\n\n"
                                       f"然后访问: http://localhost:8000/standalone_test.html")
                return False
                
            except Exception as e:
                QMessageBox.critical(self, "备用方案异常", f"备用Web服务器启动失败: {str(e)}")
                return False
                
        except Exception as e:
            QMessageBox.critical(self, "备用方案失败", f"备用方案执行失败: {str(e)}")
            return False
    
    def _ensure_web_html_files(self, web_dir):
        """确保Web HTML文件存在"""
        try:
            # 检查是否存在HTML文件
            html_files = ['standalone_test.html', 'index.html']
            
            for html_file in html_files:
                if (web_dir / html_file).exists():
                    return  # 如果有任何HTML文件存在，就返回
            
            # 如果没有HTML文件，创建一个简单的
            self._create_simple_html_file(web_dir)
            
        except Exception as e:
            print(f"确保HTML文件存在时出错: {e}")
    
    def _create_simple_html_file(self, web_dir):
        """创建简单的HTML文件"""
        try:
            html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时序违例数据展示</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #007bff; color: white; padding: 20px; text-align: center; }
        .stats { display: flex; justify-content: space-around; padding: 20px; background: #f8f9fa; }
        .stat-item { text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #495057; }
        .stat-label { color: #6c757d; margin-top: 5px; }
        .table-container { padding: 20px; overflow-x: auto; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6; }
        th { background-color: #f8f9fa; font-weight: 600; }
        tr:hover { background-color: #f8f9fa; }
        .loading { text-align: center; padding: 40px; color: #6c757d; }
        .error { background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>时序违例数据展示</h1>
            <p>基于GUI数据的简化展示</p>
        </div>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="total-violations">-</div>
                <div class="stat-label">总违例数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="total-corners">-</div>
                <div class="stat-label">Corner数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="total-cases">-</div>
                <div class="stat-label">Case数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="confirmed-violations">-</div>
                <div class="stat-label">已确认</div>
            </div>
        </div>
        
        <div class="table-container">
            <div id="loading" class="loading">正在加载数据...</div>
            <table id="violations-table" style="display: none;">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>层级路径</th>
                        <th>时间</th>
                        <th>检查信息</th>
                        <th>状态</th>
                        <th>确认人</th>
                        <th>Corner</th>
                        <th>Case</th>
                    </tr>
                </thead>
                <tbody id="violations-tbody">
                </tbody>
            </table>
        </div>
    </div>

    <script>
        async function loadData() {
            try {
                const response = await fetch('data/violations.json');
                if (!response.ok) throw new Error('数据加载失败');
                
                const data = await response.json();
                
                // 更新统计
                document.getElementById('total-violations').textContent = data.violations.length;
                document.getElementById('total-corners').textContent = data.corners.length;
                document.getElementById('total-cases').textContent = data.cases.length;
                
                const confirmedCount = data.violations.filter(v => 
                    v.status && v.status.toLowerCase().includes('confirm')).length;
                document.getElementById('confirmed-violations').textContent = confirmedCount;
                
                // 更新表格
                const tbody = document.getElementById('violations-tbody');
                data.violations.forEach(violation => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${violation.num || '-'}</td>
                        <td title="${violation.hier || ''}">${(violation.hier || '').substring(0, 50)}${(violation.hier || '').length > 50 ? '...' : ''}</td>
                        <td>${violation.time_display || (violation.time_fs ? violation.time_fs + ' fs' : '-')}</td>
                        <td title="${violation.check_info || ''}">${(violation.check_info || '').substring(0, 40)}${(violation.check_info || '').length > 40 ? '...' : ''}</td>
                        <td>${violation.status || '-'}</td>
                        <td>${violation.confirmer || '-'}</td>
                        <td>${violation.corner || '-'}</td>
                        <td>${violation.case || violation.case_name || '-'}</td>
                    `;
                    tbody.appendChild(row);
                });
                
                // 显示表格
                document.getElementById('loading').style.display = 'none';
                document.getElementById('violations-table').style.display = 'table';
                
            } catch (error) {
                document.getElementById('loading').innerHTML = 
                    `<div class="error">加载数据失败: ${error.message}</div>`;
            }
        }
        
        // 页面加载完成后加载数据
        document.addEventListener('DOMContentLoaded', loadData);
    </script>
</body>
</html>'''
            
            with open(web_dir / 'index.html', 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print("创建了简单的HTML文件")
            
        except Exception as e:
            print(f"创建简单HTML文件失败: {e}")
    
    def stop_web_display(self):
        """停止网页展示"""
        try:
            # 停止Web服务器进程
            if hasattr(self, 'web_server_process') and self.web_server_process:
                self.web_server_process.terminate()
                self.web_server_process.wait()
                self.web_server_process = None
            
            # 恢复按钮状态
            self.web_display_btn.setText("网页显示")
            self.web_display_btn.setToolTip("生成网页数据并在浏览器中打开")
            
            # 隐藏停止按钮
            if hasattr(self, 'stop_web_btn'):
                self.stop_web_btn.setVisible(False)
            
            self.status_bar.showMessage("网页服务已停止", 3000)
            QMessageBox.information(self, "成功", "网页服务已停止")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"停止网页服务失败: {str(e)}")
    
    def refresh_web_display(self):
        """刷新网页展示数据"""
        try:
            if hasattr(self, 'web_integration') and self.web_integration:
                self.web_integration.refresh_web_data()
                
                # 如果服务器在运行，重新打开浏览器
                server_status = self.web_integration.get_server_status()
                if server_status['running']:
                    import webbrowser
                    webbrowser.open(server_status['url'])
                    
        except Exception as e:
            QMessageBox.critical(self, "错误", f"刷新网页展示失败: {str(e)}")
    
    def on_web_server_status_changed(self, is_running, message):
        """Web服务器状态变化回调"""
        if is_running:
            self.status_bar.showMessage(f"网页服务: {message}", 0)
        else:
            self.status_bar.showMessage(f"网页服务: {message}", 3000)
    
    def get_all_violations(self):
        """获取所有违例数据供Web展示使用"""
        try:
            violations = []
            
            # 从数据库获取所有违例数据
            if hasattr(self, 'data_model') and self.data_model:
                all_violations = self.data_model.get_all_violations()
                
                for violation in all_violations:
                    # 转换为Web展示需要的格式
                    web_violation = {
                        'id': violation.get('id', 0),
                        'num': violation.get('num', 0),
                        'hier': violation.get('hier', ''),
                        'time_fs': violation.get('time_fs', 0),
                        'time_display': violation.get('time_display', ''),
                        'check_info': violation.get('check_info', ''),
                        'status': violation.get('status', ''),
                        'confirmer': violation.get('confirmer', ''),
                        'result': violation.get('result', ''),
                        'reason': violation.get('reason', ''),
                        'confirmed_at': violation.get('confirmed_at', ''),
                        'corner': violation.get('corner', ''),
                        'case': violation.get('case_name', ''),  # 使用case_name字段
                        'case_name': violation.get('case_name', ''),
                        'source': 'gui'
                    }
                    violations.append(web_violation)
            
            return violations
            
        except Exception as e:
            print(f"获取违例数据失败: {e}")
            return []
    
    def on_web_data_update_status(self, status_message):
        """网页数据更新状态回调"""
        self.status_bar.showMessage(f"数据更新: {status_message}", 2000)
    
    def _has_database_data(self):
        """检查数据库中是否有数据"""
        try:
            if hasattr(self, 'data_model') and self.data_model:
                violations = self.data_model.get_all_violations()
                return len(violations) > 0
            return False
        except Exception:
            return False
    
    def _auto_update_web_display(self):
        """自动更新网页展示数据"""
        try:
            # 检查是否有网页集成管理器且服务器正在运行
            if (hasattr(self, 'web_integration') and 
                self.web_integration and 
                self.web_integration.get_server_status()['running']):
                
                # 通知集成管理器有新的case加载
                corner_name = getattr(self, 'current_corner', 'unknown')
                case_name = getattr(self, 'current_case_name', 'unknown')
                
                self.web_integration.on_case_loaded(case_name, corner_name)
                
                # 在状态栏显示更新信息
                self.status_bar.showMessage("网页数据已自动更新", 3000)
                
        except Exception as e:
            print(f"自动更新网页展示失败: {e}")
            # 不显示错误对话框，避免干扰用户操作

    def _get_current_violations_for_web(self):
        """获取当前GUI中的违例数据用于网页显示"""
        try:
            violations = []
            
            # 方法1：从高性能表格获取数据
            if hasattr(self, 'use_high_performance_table') and self.use_high_performance_table:
                if hasattr(self, 'high_performance_table') and self.high_performance_table.model._data:
                    violations = self.high_performance_table.model._data
                    print(f"从高性能表格获取到 {len(violations)} 条违例记录")
            
            # 方法2：从当前违例列表获取数据
            if not violations and hasattr(self, 'current_violations') and self.current_violations:
                violations = self.current_violations
                print(f"从当前违例列表获取到 {len(violations)} 条违例记录")
            
            # 方法3：从数据库获取数据
            if not violations:
                violations = self._get_violations_smart()
                print(f"从数据库获取到 {len(violations)} 条违例记录")
            
            # 确保数据格式正确
            if violations:
                # 添加必要的字段
                for i, violation in enumerate(violations):
                    if not violation.get('id'):
                        violation['id'] = i + 1
                    if not violation.get('corner'):
                        violation['corner'] = self.get_display_corner()
                    if not violation.get('case'):
                        violation['case'] = self.current_case_name
                    if not violation.get('source'):
                        violation['source'] = 'gui'
                
                print(f"为网页显示准备了 {len(violations)} 条违例记录")
                return violations
            else:
                print("警告：没有找到任何违例数据")
                return []
                
        except Exception as e:
            print(f"获取当前违例数据失败: {e}")
            return []

    def _export_data_to_excel(self, file_path: str):
        """导出数据到Excel文件"""
        try:
            import openpyxl
            from openpyxl.styles import Font, Alignment, PatternFill
        except ImportError:
            raise ImportError("需要安装openpyxl库: pip install openpyxl")

        # 获取数据 - 使用智能corner匹配
        violations = self._get_violations_smart()

        # 调试信息
        corner_name = self.get_display_corner()
        print(f"导出Excel - 用例: {self.current_case_name}, Corner: {corner_name}")
        print(f"导出Excel - 从数据库获取到 {len(violations)} 条违例记录")

        # 如果数据库中没有数据，尝试从当前内存中的数据获取
        if not violations:
            if hasattr(self, 'current_violations') and self.current_violations:
                print("导出Excel - 数据库中无数据，使用内存中的数据")
                violations = self.current_violations
                print(f"导出Excel - 从内存获取到 {len(violations)} 条违例记录")
            elif self.use_high_performance_table and self.high_performance_table.model._data:
                print("导出Excel - 从高性能表格模型获取数据")
                violations = self.high_performance_table.model._data
                print(f"导出Excel - 从高性能表格获取到 {len(violations)} 条违例记录")

            # 调试：显示第一条记录的字段
            if violations:
                first_record = violations[0]
                print(f"导出Excel - 数据格式示例: {list(first_record.keys())[:5]}...")

        if not violations:
            raise ValueError(f"没有找到用例 '{self.current_case_name}' (corner: {corner_name}) 的违例数据。请先加载违例日志文件。")

        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "时序违例确认清单"

        # 设置表头
        headers = ["序号", "层级路径", "时间(ns)", "检查信息", "状态", "确认人", "确认结果", "确认理由", "确认时间"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        # 填充数据
        for row, violation in enumerate(violations, 2):
            # 兼容两种数据格式：数据库格式(小写)和解析器格式(大写)
            num = violation.get('num', violation.get('NUM', ''))
            hier = violation.get('hier', violation.get('Hier', ''))
            time_fs = violation.get('time_fs', violation.get('time_fs', 0))
            check_info = violation.get('check_info', violation.get('Check', ''))
            status = violation.get('status', '')
            confirmer = violation.get('confirmer', '')
            result = violation.get('result', '')
            reason = violation.get('reason', '')
            confirmed_at = violation.get('confirmed_at', '')

            print(f"导出Excel - 处理第 {row-1} 条记录: {hier[:50]}...")

            ws.cell(row=row, column=1, value=num)
            ws.cell(row=row, column=2, value=hier)
            ws.cell(row=row, column=3, value=time_fs / 1000000 if time_fs else 0)
            ws.cell(row=row, column=4, value=check_info)
            ws.cell(row=row, column=5, value=self.get_status_display(status))
            ws.cell(row=row, column=6, value=confirmer)
            ws.cell(row=row, column=7, value=self.get_result_display(result))
            ws.cell(row=row, column=8, value=reason)
            ws.cell(row=row, column=9, value=confirmed_at)

        print(f"导出Excel - 完成数据填充，共处理 {len(violations)} 条记录")

        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 保存文件
        wb.save(file_path)

    def _export_data_to_csv(self, file_path: str):
        """导出数据到CSV文件"""
        import csv

        # 获取数据 - 使用智能corner匹配
        violations = self._get_violations_smart()

        # 调试信息
        corner_name = self.get_display_corner()
        print(f"导出CSV - 用例: {self.current_case_name}, Corner: {corner_name}")
        print(f"导出CSV - 从数据库获取到 {len(violations)} 条违例记录")

        # 如果数据库中没有数据，尝试从当前内存中的数据获取
        if not violations:
            if hasattr(self, 'current_violations') and self.current_violations:
                print("导出CSV - 数据库中无数据，使用内存中的数据")
                violations = self.current_violations
                print(f"导出CSV - 从内存获取到 {len(violations)} 条违例记录")
            elif self.use_high_performance_table and self.high_performance_table.model._data:
                print("导出CSV - 从高性能表格模型获取数据")
                violations = self.high_performance_table.model._data
                print(f"导出CSV - 从高性能表格获取到 {len(violations)} 条违例记录")

            # 调试：显示第一条记录的字段
            if violations:
                first_record = violations[0]
                print(f"导出CSV - 数据格式示例: {list(first_record.keys())[:5]}...")

        if not violations:
            raise ValueError(f"没有找到用例 '{self.current_case_name}' (corner: {corner_name}) 的违例数据。请先加载违例日志文件。")

        # 写入CSV文件
        with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)

            # 写入表头
            headers = ["序号", "层级路径", "时间(ns)", "检查信息", "状态", "确认人", "确认结果", "确认理由", "确认时间"]
            writer.writerow(headers)

            # 写入数据
            for i, violation in enumerate(violations):
                # 兼容两种数据格式：数据库格式(小写)和解析器格式(大写)
                num = violation.get('num', violation.get('NUM', ''))
                hier = violation.get('hier', violation.get('Hier', ''))
                time_fs = violation.get('time_fs', violation.get('time_fs', 0))
                check_info = violation.get('check_info', violation.get('Check', ''))
                status = violation.get('status', '')
                confirmer = violation.get('confirmer', '')
                result = violation.get('result', '')
                reason = violation.get('reason', '')
                confirmed_at = violation.get('confirmed_at', '')

                print(f"导出CSV - 处理第 {i+1} 条记录: {hier[:50]}...")

                row = [
                    num,
                    hier,
                    time_fs / 1000000 if time_fs else 0,
                    check_info,
                    self.get_status_display(status),
                    confirmer,
                    self.get_result_display(result),
                    reason,
                    confirmed_at
                ]
                writer.writerow(row)

            print(f"导出CSV - 完成数据写入，共处理 {len(violations)} 条记录")


class ConfirmationDialog(QDialog):
    """确认对话框"""

    def __init__(self, parent=None, violation=None, suggestions=None, edit_mode=False):
        super().__init__(parent)
        self.violation = violation
        self.suggestions = suggestions
        self.edit_mode = edit_mode

        self.setWindowTitle("编辑确认信息" if edit_mode else "确认时序违例")
        self.setModal(True)
        self.resize(600, 400)

        # 设置窗口标志，确保对话框正常显示
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        self.init_ui()
        if not edit_mode:  # 只在非编辑模式下应用建议
            self.apply_suggestions()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        # 违例信息显示
        if self.violation:
            info_group = QGroupBox("违例信息")
            info_layout = QFormLayout(info_group)

            info_layout.addRow("序号:", QLabel(str(self.violation.get('num', ''))))
            info_layout.addRow("层级路径:", QLabel(self.violation.get('hier', '')))

            time_ns = self.violation.get('time_fs', 0) / 1000000
            info_layout.addRow("时间:", QLabel(f"{time_ns:.3f} ns"))

            check_label = QLabel(self.violation.get('check_info', ''))
            check_label.setWordWrap(True)
            info_layout.addRow("检查信息:", check_label)

            layout.addWidget(info_group)

        # 确认信息输入
        confirm_group = QGroupBox("确认信息")
        confirm_layout = QFormLayout(confirm_group)

        # 确认人
        self.confirmer_edit = QLineEdit()
        self.confirmer_edit.setPlaceholderText("请输入确认人姓名")
        confirm_layout.addRow("确认人*:", self.confirmer_edit)

        # 确认结果
        result_layout = QHBoxLayout()
        self.result_group = QButtonGroup()

        self.pass_radio = QRadioButton("通过")
        self.issue_radio = QRadioButton("有问题")
        self.pass_radio.setChecked(True)  # 默认选择通过

        self.result_group.addButton(self.pass_radio, 0)
        self.result_group.addButton(self.issue_radio, 1)

        result_layout.addWidget(self.pass_radio)
        result_layout.addWidget(self.issue_radio)
        result_layout.addStretch()

        confirm_layout.addRow("确认结果*:", result_layout)

        # 确认理由
        self.reason_edit = QTextEdit()
        self.reason_edit.setPlaceholderText("请输入确认理由或解决方案")
        self.reason_edit.setMaximumHeight(100)
        confirm_layout.addRow("确认理由*:", self.reason_edit)

        layout.addWidget(confirm_group)

        # 历史建议
        if self.suggestions and not self.edit_mode:
            suggestion_group = QGroupBox("历史建议")
            suggestion_layout = QVBoxLayout(suggestion_group)

            suggestion_text = f"确认人: {self.suggestions.get('confirmer', '')}\n"
            suggestion_text += f"确认结果: {self.suggestions.get('result', '')}\n"
            suggestion_text += f"确认理由: {self.suggestions.get('reason', '')}\n"
            suggestion_text += f"使用次数: {self.suggestions.get('match_count', 0)}"

            suggestion_label = QLabel(suggestion_text)
            suggestion_label.setWordWrap(True)
            suggestion_layout.addWidget(suggestion_label)

            apply_btn = QPushButton("应用建议")
            apply_btn.clicked.connect(self.apply_suggestions)
            suggestion_layout.addWidget(apply_btn)

            layout.addWidget(suggestion_group)

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # 如果是编辑模式，填充现有数据
        if self.edit_mode and self.violation:
            self.confirmer_edit.setText(self.violation.get('confirmer', ''))

            result = self.violation.get('result', '')
            if result == 'pass':
                self.pass_radio.setChecked(True)
            elif result == 'issue':
                self.issue_radio.setChecked(True)

            self.reason_edit.setPlainText(self.violation.get('reason', ''))

    def apply_suggestions(self):
        """应用历史建议"""
        if not self.suggestions:
            return

        self.confirmer_edit.setText(self.suggestions.get('confirmer', ''))

        result = self.suggestions.get('result', '')
        if result == 'pass':
            self.pass_radio.setChecked(True)
        elif result == 'issue':
            self.issue_radio.setChecked(True)

        self.reason_edit.setPlainText(self.suggestions.get('reason', ''))

    def accept(self):
        """确认按钮点击（移除验证弹窗）"""
        # 静默验证输入，不显示弹窗
        if not self.confirmer_edit.text().strip():
            print("警告: 请输入确认人姓名")
            # 设置焦点到确认人输入框
            self.confirmer_edit.setFocus()
            return

        if not self.reason_edit.toPlainText().strip():
            print("警告: 请输入确认理由")
            # 设置焦点到理由输入框
            self.reason_edit.setFocus()
            return

        super().accept()

    def get_result(self):
        """获取确认结果"""
        result = 'pass' if self.pass_radio.isChecked() else 'issue'

        return {
            'confirmer': self.confirmer_edit.text().strip(),
            'result': result,
            'reason': self.reason_edit.toPlainText().strip()
        }


class BatchConfirmationDialog(QDialog):
    """批量确认对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("批量确认")
        self.setModal(True)
        self.resize(400, 250)

        # 设置窗口标志，确保对话框正常显示
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        # 说明
        info_label = QLabel("请填写批量确认信息，将应用到所有选中的违例记录：")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 确认信息输入
        form_layout = QFormLayout()

        # 确认人
        self.confirmer_edit = QLineEdit()
        self.confirmer_edit.setPlaceholderText("请输入确认人姓名")
        form_layout.addRow("确认人*:", self.confirmer_edit)

        # 确认结果
        result_layout = QHBoxLayout()
        self.result_group = QButtonGroup()

        self.pass_radio = QRadioButton("通过")
        self.issue_radio = QRadioButton("有问题")
        self.pass_radio.setChecked(True)  # 默认选择通过

        self.result_group.addButton(self.pass_radio, 0)
        self.result_group.addButton(self.issue_radio, 1)

        result_layout.addWidget(self.pass_radio)
        result_layout.addWidget(self.issue_radio)
        result_layout.addStretch()

        form_layout.addRow("确认结果*:", result_layout)

        # 确认理由
        self.reason_edit = QTextEdit()
        self.reason_edit.setPlaceholderText("请输入确认理由或解决方案")
        self.reason_edit.setMaximumHeight(80)
        form_layout.addRow("确认理由*:", self.reason_edit)

        layout.addLayout(form_layout)

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def accept(self):
        """确认按钮点击（移除验证弹窗）"""
        # 静默验证输入，不显示弹窗
        if not self.confirmer_edit.text().strip():
            print("警告: 请输入确认人姓名")
            # 设置焦点到确认人输入框
            self.confirmer_edit.setFocus()
            return

        if not self.reason_edit.toPlainText().strip():
            print("警告: 请输入确认理由")
            # 设置焦点到理由输入框
            self.reason_edit.setFocus()
            return

        super().accept()

    def get_result(self):
        """获取确认结果"""
        result = 'pass' if self.pass_radio.isChecked() else 'issue'

        return {
            'confirmer': self.confirmer_edit.text().strip(),
            'result': result,
            'reason': self.reason_edit.toPlainText().strip()
        }

class HistoryManagementDialog(QDialog):
    """历史管理对话框"""

    def __init__(self, parent=None, data_model=None):
        super().__init__(parent)
        self.data_model = data_model

        self.setWindowTitle("历史确认模式管理")
        self.setModal(True)
        self.resize(1000, 600)

        # 设置窗口标志
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        self.init_ui()
        self.load_patterns()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        # 说明标签
        info_label = QLabel("历史确认模式管理 - 显示所有已保存的确认模式，可以查看和删除")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 工具栏
        toolbar_layout = QHBoxLayout()

        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_patterns)
        toolbar_layout.addWidget(self.refresh_btn)

        # 导出功能按钮
        self.export_excel_btn = QPushButton("导出Excel")
        self.export_excel_btn.setToolTip("导出历史确认模式到Excel文件")
        self.export_excel_btn.clicked.connect(self.export_to_excel)
        toolbar_layout.addWidget(self.export_excel_btn)

        self.export_csv_btn = QPushButton("导出CSV")
        self.export_csv_btn.setToolTip("导出历史确认模式到CSV文件")
        self.export_csv_btn.clicked.connect(self.export_to_csv)
        toolbar_layout.addWidget(self.export_csv_btn)

        self.clear_all_btn = QPushButton("清除全部")
        self.clear_all_btn.clicked.connect(self.clear_all_patterns)
        toolbar_layout.addWidget(self.clear_all_btn)

        toolbar_layout.addStretch()

        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)
        toolbar_layout.addWidget(self.close_btn)

        layout.addLayout(toolbar_layout)

        # 历史模式表格
        self.patterns_table = QTableWidget()
        self.patterns_table.setColumnCount(7)

        headers = ["层级路径", "检查信息", "确认人", "确认结果", "确认理由", "使用次数", "最后使用时间"]
        self.patterns_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.patterns_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.patterns_table.setAlternatingRowColors(True)
        self.patterns_table.setSortingEnabled(True)

        # 设置列宽
        header = self.patterns_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # 层级路径
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 检查信息
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # 确认人
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # 确认结果
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # 确认理由
        header.setSectionResizeMode(5, QHeaderView.Fixed)    # 使用次数
        header.setSectionResizeMode(6, QHeaderView.Fixed)    # 最后使用时间

        # 设置固定列宽
        self.patterns_table.setColumnWidth(2, 100)  # 确认人
        self.patterns_table.setColumnWidth(3, 80)   # 确认结果
        self.patterns_table.setColumnWidth(5, 80)   # 使用次数
        self.patterns_table.setColumnWidth(6, 150)  # 最后使用时间

        layout.addWidget(self.patterns_table)

        # 统计信息
        self.stats_label = QLabel("总计: 0 个历史模式")
        layout.addWidget(self.stats_label)

    def load_patterns(self):
        """加载历史模式"""
        try:
            patterns = self.data_model.get_all_patterns()

            # 设置表格行数
            self.patterns_table.setRowCount(len(patterns))

            # 填充数据
            for row, pattern in enumerate(patterns):
                # 层级路径
                hier_item = QTableWidgetItem(pattern.get('hier_pattern', ''))
                self.patterns_table.setItem(row, 0, hier_item)

                # 检查信息
                check_item = QTableWidgetItem(pattern.get('check_pattern', ''))
                self.patterns_table.setItem(row, 1, check_item)

                # 确认人
                confirmer_item = QTableWidgetItem(pattern.get('default_confirmer', ''))
                confirmer_item.setTextAlignment(Qt.AlignCenter)
                self.patterns_table.setItem(row, 2, confirmer_item)

                # 确认结果
                result = pattern.get('default_result', '')
                result_display = "通过" if result == "pass" else "有问题" if result == "issue" else result
                result_item = QTableWidgetItem(result_display)
                result_item.setTextAlignment(Qt.AlignCenter)
                self.patterns_table.setItem(row, 3, result_item)

                # 确认理由
                reason_item = QTableWidgetItem(pattern.get('default_reason', ''))
                self.patterns_table.setItem(row, 4, reason_item)

                # 使用次数
                count_item = QTableWidgetItem(str(pattern.get('match_count', 0)))
                count_item.setTextAlignment(Qt.AlignCenter)
                self.patterns_table.setItem(row, 5, count_item)

                # 最后使用时间
                last_used = pattern.get('last_used', '')
                time_item = QTableWidgetItem(last_used)
                time_item.setTextAlignment(Qt.AlignCenter)
                self.patterns_table.setItem(row, 6, time_item)

            # 更新统计信息
            self.stats_label.setText(f"总计: {len(patterns)} 个历史模式")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载历史模式失败: {str(e)}")

    def export_to_excel(self):
        """导出历史确认模式到Excel"""
        try:
            # 检查是否有数据可导出
            patterns = self.data_model.get_all_patterns()
            if not patterns:
                QMessageBox.information(self, "提示", "没有历史确认模式可导出")
                return

            # 创建默认导出目录
            import os
            default_dir = os.path.join(os.getcwd(), "VIOLATION_CHECK")
            os.makedirs(default_dir, exist_ok=True)

            # 生成默认文件名（包含时间戳）
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"timing_violation_history_{timestamp}.xlsx"
            default_path = os.path.join(default_dir, default_filename)

            # 选择保存路径
            from PyQt5.QtWidgets import QFileDialog
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出Excel文件", default_path, "Excel文件 (*.xlsx)"
            )

            if file_path:
                success = self.data_model.export_patterns_to_excel(file_path)
                if success:
                    QMessageBox.information(self, "成功", f"历史确认模式已导出到:\n{file_path}")
                else:
                    QMessageBox.warning(self, "警告", "导出Excel文件失败")

        except ImportError:
            QMessageBox.critical(self, "错误", "需要安装openpyxl库才能导出Excel文件\n请运行: pip install openpyxl")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出Excel失败: {str(e)}")

    def export_to_csv(self):
        """导出历史确认模式到CSV"""
        try:
            # 检查是否有数据可导出
            patterns = self.data_model.get_all_patterns()
            if not patterns:
                QMessageBox.information(self, "提示", "没有历史确认模式可导出")
                return

            # 创建默认导出目录
            import os
            default_dir = os.path.join(os.getcwd(), "VIOLATION_CHECK")
            os.makedirs(default_dir, exist_ok=True)

            # 生成默认文件名（包含时间戳）
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"timing_violation_history_{timestamp}.csv"
            default_path = os.path.join(default_dir, default_filename)

            # 选择保存路径
            from PyQt5.QtWidgets import QFileDialog
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出CSV文件", default_path, "CSV文件 (*.csv)"
            )

            if file_path:
                success = self.data_model.export_patterns_to_csv(file_path)
                if success:
                    QMessageBox.information(self, "成功", f"历史确认模式已导出到:\n{file_path}")
                else:
                    QMessageBox.warning(self, "警告", "导出CSV文件失败")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出CSV失败: {str(e)}")

    def clear_all_patterns(self):
        """清除所有历史模式"""
        reply = QMessageBox.question(
            self, "确认",
            "确定要清除所有历史确认模式吗？\n此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                success = self.data_model.clear_all_patterns()
                if success:
                    QMessageBox.information(self, "成功", "已清除所有历史模式")
                    self.load_patterns()  # 重新加载
                else:
                    QMessageBox.warning(self, "警告", "清除历史模式失败")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"清除历史模式失败: {str(e)}")


class DatabaseMergeDialog(QDialog):
    """数据库合并对话框"""

    def __init__(self, data_model, parent=None):
        super().__init__(parent)
        self.data_model = data_model
        self.selected_databases = []
        self.merge_thread = None
        self.stats_thread = None
        self.init_ui()
        self.setModal(True)

        # 异步加载当前数据库信息
        self.load_current_db_info_async()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("数据库合并")
        self.setFixedSize(600, 500)

        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)

        # 标题和说明
        title_label = QLabel("数据库合并")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50;")
        layout.addWidget(title_label)

        desc_label = QLabel(
            "将其他用户的时序违例数据库合并到当前数据库中，共享违例确认信息。\n"
            "合并前会自动备份当前数据库，确保数据安全。"
        )
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: #7f8c8d; margin-bottom: 10px;")
        layout.addWidget(desc_label)

        # 当前数据库信息
        current_group = QGroupBox("当前数据库信息")
        current_layout = QVBoxLayout(current_group)

        self.current_db_label = QLabel("正在加载数据库信息...")
        self.current_db_label.setStyleSheet("color: #7f8c8d; font-style: italic;")
        current_layout.addWidget(self.current_db_label)

        layout.addWidget(current_group)

        # 源数据库选择
        source_group = QGroupBox("选择要合并的数据库")
        source_layout = QVBoxLayout(source_group)

        # 文件选择按钮
        file_select_layout = QHBoxLayout()
        self.add_db_btn = QPushButton("添加数据库文件...")
        self.add_db_btn.clicked.connect(self.add_database_file)
        file_select_layout.addWidget(self.add_db_btn)

        self.clear_list_btn = QPushButton("清空列表")
        self.clear_list_btn.clicked.connect(self.clear_database_list)
        file_select_layout.addWidget(self.clear_list_btn)

        file_select_layout.addStretch()
        source_layout.addLayout(file_select_layout)

        # 数据库列表
        self.db_list_widget = QListWidget()
        self.db_list_widget.setMinimumHeight(150)
        source_layout.addWidget(self.db_list_widget)

        layout.addWidget(source_group)

        # 进度显示
        progress_group = QGroupBox("合并进度")
        progress_layout = QVBoxLayout(progress_group)

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)

        self.progress_label = QLabel("")
        self.progress_label.setVisible(False)
        progress_layout.addWidget(self.progress_label)

        layout.addWidget(progress_group)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.merge_btn = QPushButton("开始合并")
        self.merge_btn.clicked.connect(self.start_merge)
        self.merge_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        button_layout.addWidget(self.merge_btn)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)

    def load_current_db_info_async(self):
        """异步加载当前数据库信息"""
        self.stats_thread = DatabaseStatsThread(self.data_model)
        self.stats_thread.stats_loaded.connect(self.update_current_db_info)
        self.stats_thread.stats_error.connect(self.handle_stats_error)
        self.stats_thread.start()

    def update_current_db_info(self, stats):
        """更新当前数据库信息"""
        try:
            info_text = (
                f"数据库路径: {self.data_model.db_path}\n"
                f"总违例数: {stats['total_violations']}\n"
                f"已确认: {stats['confirmed_violations']}, "
                f"待确认: {stats['pending_violations']}\n"
                f"历史模式: {stats['total_patterns']}, "
                f"涉及用例: {stats['unique_cases']}"
            )
            self.current_db_label.setText(info_text)
            self.current_db_label.setStyleSheet("")  # 清除加载样式
        except Exception as e:
            self.current_db_label.setText(f"显示数据库信息失败: {str(e)}")

    def handle_stats_error(self, error_msg):
        """处理统计信息加载错误"""
        self.current_db_label.setText(f"获取数据库信息失败: {error_msg}")
        self.current_db_label.setStyleSheet("color: #e74c3c;")

    def add_database_file(self):
        """添加数据库文件"""
        file_dialog = QFileDialog(self)
        file_dialog.setWindowTitle("选择时序违例数据库文件")
        file_dialog.setNameFilter("SQLite数据库文件 (*.db);;所有文件 (*)")
        file_dialog.setFileMode(QFileDialog.ExistingFiles)

        if file_dialog.exec_() == QFileDialog.Accepted:
            selected_files = file_dialog.selectedFiles()
            for file_path in selected_files:
                if file_path not in self.selected_databases:
                    # 验证数据库文件
                    if self.validate_database_file(file_path):
                        self.selected_databases.append(file_path)
                        self.update_database_list()
                    else:
                        QMessageBox.warning(
                            self, "警告",
                            f"数据库文件无效或不兼容:\n{file_path}"
                        )

    def validate_database_file(self, file_path: str) -> bool:
        """验证数据库文件"""
        try:
            # 检查是否是当前数据库
            if os.path.abspath(file_path) == os.path.abspath(self.data_model.db_path):
                QMessageBox.warning(self, "警告", "不能选择当前数据库文件")
                return False

            # 验证schema兼容性
            return self.data_model.validate_database_schema(file_path)
        except Exception as e:
            print(f"验证数据库文件失败: {str(e)}")
            return False

    def update_database_list(self):
        """更新数据库列表显示"""
        self.db_list_widget.clear()

        # 先添加占位项，显示正在加载状态
        for db_path in self.selected_databases:
            item_text = (
                f"{os.path.basename(db_path)}\n"
                f"路径: {db_path}\n"
                f"正在加载统计信息..."
            )
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, db_path)
            item.setForeground(QColor("#7f8c8d"))
            self.db_list_widget.addItem(item)

        # 异步加载统计信息
        if self.selected_databases:
            self.list_stats_thread = DatabaseListStatsThread(self.data_model, self.selected_databases)
            self.list_stats_thread.item_stats_loaded.connect(self.update_database_item)
            self.list_stats_thread.start()

    def update_database_item(self, db_path, stats, error_msg):
        """更新单个数据库项的显示"""
        # 找到对应的列表项
        for i in range(self.db_list_widget.count()):
            item = self.db_list_widget.item(i)
            if item.data(Qt.UserRole) == db_path:
                if error_msg:
                    item_text = f"{os.path.basename(db_path)}\n路径: {db_path}\n错误: {error_msg}"
                    item.setForeground(QColor("#e74c3c"))
                else:
                    item_text = (
                        f"{os.path.basename(db_path)}\n"
                        f"路径: {db_path}\n"
                        f"违例数: {stats['total_violations']}, "
                        f"已确认: {stats['confirmed_violations']}, "
                        f"模式数: {stats['total_patterns']}"
                    )
                    item.setForeground(QColor("#2c3e50"))

                item.setText(item_text)
                break

    def clear_database_list(self):
        """清空数据库列表"""
        self.selected_databases.clear()
        self.db_list_widget.clear()

    def start_merge(self):
        """开始合并数据库"""
        if not self.selected_databases:
            QMessageBox.warning(self, "警告", "请先选择要合并的数据库文件")
            return

        # 确认对话框
        reply = QMessageBox.question(
            self, "确认合并",
            f"即将合并 {len(self.selected_databases)} 个数据库到当前数据库。\n"
            "合并前会自动备份当前数据库。\n\n"
            "是否继续？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # 禁用按钮，显示进度
        self.merge_btn.setEnabled(False)
        self.add_db_btn.setEnabled(False)
        self.clear_list_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_label.setVisible(True)
        self.progress_bar.setRange(0, len(self.selected_databases))
        self.progress_bar.setValue(0)

        # 启动合并线程
        self.merge_thread = DatabaseMergeThread(
            self.data_model, self.selected_databases
        )
        self.merge_thread.progress_updated.connect(self.update_progress)
        self.merge_thread.merge_completed.connect(self.merge_finished)
        self.merge_thread.start()

    def update_progress(self, current, total, message):
        """更新进度显示"""
        self.progress_bar.setValue(current)
        self.progress_label.setText(message)

    def merge_finished(self, result):
        """合并完成处理"""
        # 恢复按钮状态
        self.merge_btn.setEnabled(True)
        self.add_db_btn.setEnabled(True)
        self.clear_list_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.progress_label.setVisible(False)

        # 显示结果
        if result['success']:
            message = (
                f"数据库合并成功！\n\n"
                f"处理数据库: {result['processed_databases']}\n"
                f"新增违例: {result['total_violations_added']}\n"
                f"更新确认: {result['total_confirmations_updated']}\n"
                f"合并模式: {result['total_patterns_merged']}\n"
                f"备份文件: {os.path.basename(result['backup_path'])}"
            )

            if result['errors']:
                message += f"\n\n警告信息:\n" + "\n".join(result['errors'][:3])
                if len(result['errors']) > 3:
                    message += f"\n... 还有 {len(result['errors']) - 3} 个警告"

            QMessageBox.information(self, "合并成功", message)

            # 重新异步加载当前数据库信息
            self.load_current_db_info_async()

            # 询问是否关闭对话框
            reply = QMessageBox.question(
                self, "完成", "合并已完成，是否关闭此对话框？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            if reply == QMessageBox.Yes:
                self.accept()

    def closeEvent(self, event):
        """对话框关闭事件处理"""
        # 清理线程资源
        if hasattr(self, 'stats_thread') and self.stats_thread and self.stats_thread.isRunning():
            self.stats_thread.quit()
            self.stats_thread.wait(1000)  # 等待最多1秒

        if hasattr(self, 'list_stats_thread') and self.list_stats_thread and self.list_stats_thread.isRunning():
            self.list_stats_thread.quit()
            self.list_stats_thread.wait(1000)

        if self.merge_thread and self.merge_thread.isRunning():
            self.merge_thread.quit()
            self.merge_thread.wait(1000)

            super().closeEvent(event)
        else:
            error_message = "数据库合并失败！\n\n错误信息:\n" + "\n".join(result['errors'])
            QMessageBox.critical(self, "合并失败", error_message)


class DatabaseMergeThread(QThread):
    """数据库合并线程"""

    progress_updated = pyqtSignal(int, int, str)
    merge_completed = pyqtSignal(dict)

    def __init__(self, data_model, source_databases):
        super().__init__()
        self.data_model = data_model
        self.source_databases = source_databases

    def run(self):
        """执行合并"""
        try:
            result = self.data_model.merge_databases(
                self.source_databases,
                progress_callback=self.progress_updated.emit
            )
            self.merge_completed.emit(result)
        except Exception as e:
            error_result = {
                'success': False,
                'errors': [f"合并过程中发生异常: {str(e)}"],
                'backup_path': '',
                'total_violations_added': 0,
                'total_confirmations_updated': 0,
                'total_patterns_merged': 0,
                'processed_databases': 0
            }
            self.merge_completed.emit(error_result)


class DatabaseStatsThread(QThread):
    """数据库统计信息加载线程"""

    stats_loaded = pyqtSignal(dict)
    stats_error = pyqtSignal(str)

    def __init__(self, data_model, db_path=None):
        super().__init__()
        self.data_model = data_model
        self.db_path = db_path

    def run(self):
        """执行统计信息加载"""
        try:
            stats = self.data_model.get_database_statistics(self.db_path)
            self.stats_loaded.emit(stats)
        except Exception as e:
            self.stats_error.emit(str(e))


class DatabaseListStatsThread(QThread):
    """数据库列表统计信息加载线程"""

    item_stats_loaded = pyqtSignal(str, dict, str)  # db_path, stats, error_msg

    def __init__(self, data_model, db_paths):
        super().__init__()
        self.data_model = data_model
        self.db_paths = db_paths

    def run(self):
        """执行列表统计信息加载"""
        for db_path in self.db_paths:
            try:
                stats = self.data_model.get_database_statistics(db_path)
                self.item_stats_loaded.emit(db_path, stats, "")
            except Exception as e:
                self.item_stats_loaded.emit(db_path, {}, str(e))
