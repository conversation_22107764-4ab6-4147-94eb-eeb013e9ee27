#!/usr/bin/env python3
"""
测试GUI Web展示功能 - 修复版

验证修复后的Web展示功能是否正常工作。
"""

import sys
import time
from pathlib import Path

def test_gui_web_launcher():
    """测试GUI Web启动器"""
    print("🧪 测试GUI Web启动器...")
    
    try:
        from gui_web_launcher_fixed import launch_web_display_for_gui
        
        # 创建测试数据
        test_violations = [
            {
                'id': 1,
                'num': 1,
                'hier': 'cpu.core0.signal_a',
                'time_fs': 1000000,
                'check_info': 'Setup time violation',
                'status': 'confirmed',
                'confirmer': 'test_user',
                'result': 