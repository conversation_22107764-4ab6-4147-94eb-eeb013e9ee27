#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试性能修复效果
验证12000条记录是否正确使用高性能表格
"""

import sys
import os
import time
from typing import Dict, List

# 添加插件路径
plugin_path = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, plugin_path)

def test_strategy_selection():
    """测试策略选择逻辑"""
    print("=" * 60)
    print("测试策略选择逻辑")
    print("=" * 60)
    
    try:
        from strategy_manager import IntelligentStrategyManager, StrategyType
        from performance_optimizer import PerformanceOptimizer
        
        # 创建性能优化器和策略管理器
        performance_optimizer = PerformanceOptimizer()
        strategy_manager = IntelligentStrategyManager(performance_optimizer)
        
        # 测试12000条记录的策略选择
        violation_count = 12000
        system_capabilities = {
            'memory_gb': 8,
            'cpu_cores': 4,
            'optimal_batch_size': 2000
        }
        performance_level = "good"
        
        print(f"测试违例数量: {violation_count:,}")
        print(f"系统能力: {system_capabilities}")
        print(f"性能等级: {performance_level}")
        print()
        
        # 选择策略
        strategy_config = strategy_manager.select_strategy_by_violation_count(
            violation_count, system_capabilities, performance_level
        )
        
        print("策略选择结果:")
        print(f"  策略名称: {strategy_config.strategy_name}")
        print(f"  显示模式: {strategy_config.display_mode}")
        print(f"  解析器类型: {strategy_config.parser_type}")
        print(f"  使用分页: {strategy_config.use_pagination}")
        print(f"  页面大小: {strategy_config.page_size}")
        print(f"  批处理大小: {strategy_config.batch_size}")
        print(f"  内存限制: {strategy_config.memory_limit_mb}MB")
        print(f"  选择原因: {strategy_config.selection_reason}")
        print()
        
        # 验证策略是否正确
        expected_display_mode = 'high_performance_table'
        if strategy_config.display_mode == expected_display_mode:
            print("✓ 策略选择正确: 12000条记录应该使用高性能表格")
        else:
            print(f"✗ 策略选择错误: 期望 {expected_display_mode}, 实际 {strategy_config.display_mode}")
        
        return strategy_config
        
    except Exception as e:
        print(f"✗ 策略选择测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_fallback_mechanism():
    """测试回退机制"""
    print("=" * 60)
    print("测试回退机制")
    print("=" * 60)
    
    try:
        from strategy_manager import IntelligentStrategyManager, TriggerCondition
        from performance_optimizer import PerformanceOptimizer
        
        # 创建策略管理器
        performance_optimizer = PerformanceOptimizer()
        strategy_manager = IntelligentStrategyManager(performance_optimizer)
        
        # 首先选择一个策略
        violation_count = 12000
        system_capabilities = {'memory_gb': 8, 'cpu_cores': 4}
        strategy_config = strategy_manager.select_strategy_by_violation_count(
            violation_count, system_capabilities, "good"
        )
        strategy_manager.current_strategy = strategy_config
        
        print(f"初始策略: {strategy_config.strategy_name}")
        print(f"初始显示模式: {strategy_config.display_mode}")
        print()
        
        # 模拟性能降级情况
        current_metrics = {
            'load_time': 13.5,  # 13.5秒加载时间
            'memory_usage_mb': 400,
            'record_count': 12000,
            'ui_response_time': 13.5,
            'error_rate': 0.0
        }
        
        print("模拟性能降级情况:")
        print(f"  加载时间: {current_metrics['load_time']}秒")
        print(f"  内存使用: {current_metrics['memory_usage_mb']}MB")
        print(f"  记录数量: {current_metrics['record_count']:,}")
        print()
        
        # 触发回退机制
        fallback_strategy = strategy_manager.trigger_fallback(
            TriggerCondition.PERFORMANCE_DEGRADATION.value, current_metrics
        )
        
        if fallback_strategy:
            print("回退策略结果:")
            print(f"  策略名称: {fallback_strategy.strategy_name}")
            print(f"  显示模式: {fallback_strategy.display_mode}")
            print(f"  强制高性能: {getattr(fallback_strategy, 'force_high_performance', False)}")
            print(f"  页面大小: {fallback_strategy.page_size}")
            print(f"  选择原因: {fallback_strategy.selection_reason}")
            print()
            
            # 验证回退策略
            if fallback_strategy.display_mode == 'high_performance_table':
                print("✓ 回退策略正确: 使用高性能表格")
            else:
                print(f"✗ 回退策略可能有问题: 显示模式为 {fallback_strategy.display_mode}")
        else:
            print("✗ 回退机制失败: 没有找到合适的回退策略")
        
        return fallback_strategy
        
    except Exception as e:
        print(f"✗ 回退机制测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_threshold_logic():
    """测试阈值逻辑"""
    print("=" * 60)
    print("测试阈值逻辑")
    print("=" * 60)
    
    test_cases = [
        (1000, "应该使用标准表格"),
        (5000, "应该使用高性能表格"),
        (12000, "应该使用高性能表格"),
        (20000, "应该使用高性能表格"),
        (50000, "应该使用高性能表格")
    ]
    
    try:
        from strategy_manager import IntelligentStrategyManager
        from performance_optimizer import PerformanceOptimizer
        
        performance_optimizer = PerformanceOptimizer()
        strategy_manager = IntelligentStrategyManager(performance_optimizer)
        
        for violation_count, expected_behavior in test_cases:
            print(f"测试 {violation_count:,} 条记录:")
            
            # 检查基础策略类型
            base_strategy = strategy_manager._determine_base_strategy(violation_count)
            print(f"  基础策略: {base_strategy.value}")
            
            # 选择完整策略
            system_caps = {'memory_gb': 8, 'cpu_cores': 4}
            strategy_config = strategy_manager.select_strategy_by_violation_count(
                violation_count, system_caps, "good"
            )
            
            print(f"  显示模式: {strategy_config.display_mode}")
            print(f"  期望行为: {expected_behavior}")
            
            # 验证结果
            if violation_count >= 5000:
                if strategy_config.display_mode in ['high_performance_table', 'virtual_table_with_lazy_loading', 'paginated_virtual_table']:
                    print("  ✓ 正确")
                else:
                    print("  ✗ 错误: 应该使用高性能表格")
            else:
                if strategy_config.display_mode == 'standard_table':
                    print("  ✓ 正确")
                else:
                    print("  ✓ 可接受: 使用高性能表格也没问题")
            
            print()
            
    except Exception as e:
        print(f"✗ 阈值逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("时序违例插件性能修复测试")
    print("测试目标: 确保12000条记录正确使用高性能表格")
    print()
    
    # 测试策略选择
    strategy_config = test_strategy_selection()
    
    # 测试回退机制
    fallback_strategy = test_fallback_mechanism()
    
    # 测试阈值逻辑
    test_threshold_logic()
    
    print("=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if strategy_config and strategy_config.display_mode == 'high_performance_table':
        print("✓ 策略选择测试通过")
    else:
        print("✗ 策略选择测试失败")
    
    if fallback_strategy and fallback_strategy.display_mode == 'high_performance_table':
        print("✓ 回退机制测试通过")
    else:
        print("✗ 回退机制测试失败")
    
    print()
    print("修复建议:")
    print("1. 确保12000条记录使用高性能表格")
    print("2. 调整性能检查阈值，避免过早触发回退")
    print("3. 回退策略应该优先使用高性能表格")
    print("4. 降低主窗口的performance_threshold到5000")

if __name__ == "__main__":
    main()
