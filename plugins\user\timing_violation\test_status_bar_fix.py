#!/usr/bin/env python3
"""
测试状态栏修复

验证TimingViolationWindow的status_bar属性是否正常工作。
"""

import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent.parent
sys.path.insert(0, str(project_root))

def test_status_bar_creation():
    """测试状态栏创建"""
    print("🧪 测试状态栏创建...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        
        # 创建QApplication实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 导入TimingViolationWindow
        from plugins.user.timing_violation.main_window import TimingViolationWindow
        
        # 创建窗口实例
        print("  创建TimingViolationWindow实例...")
        window = TimingViolationWindow()
        
        # 检查status_bar属性是否存在
        if hasattr(window, 'status_bar'):
            print("  ✅ status_bar属性存在")
        else:
            print("  ❌ status_bar属性不存在")
            return False
        
        # 检查showMessage方法是否存在
        if hasattr(window.status_bar, 'showMessage'):
            print("  ✅ showMessage方法存在")
        else:
            print("  ❌ showMessage方法不存在")
            return False
        
        # 测试showMessage方法
        try:
            window.status_bar.showMessage("测试消息", 1000)
            print("  ✅ showMessage方法调用成功")
        except Exception as e:
            print(f"  ❌ showMessage方法调用失败: {e}")
            return False
        
        # 测试其他状态栏组件
        required_components = ['status_label', 'stats_label', 'perf_label', 'time_label']
        for component in required_components:
            if hasattr(window, component):
                print(f"  ✅ {component}组件存在")
            else:
                print(f"  ❌ {component}组件不存在")
                return False
        
        print("✅ 状态栏创建测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 状态栏创建测试失败: {e}")
        return False


def test_web_display_methods():
    """测试Web展示相关方法"""
    print("\n🧪 测试Web展示相关方法...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        
        # 创建QApplication实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 导入TimingViolationWindow
        from plugins.user.timing_violation.main_window import TimingViolationWindow
        
        # 创建窗口实例
        window = TimingViolationWindow()
        
        # 检查Web展示相关方法是否存在
        web_methods = [
            'handle_web_display_click',
            'start_web_display', 
            'launch_web_display_simple',
            'stop_web_display',
            'get_all_violations',
            '_launch_web_display_fallback'
        ]
        
        for method_name in web_methods:
            if hasattr(window, method_name):
                print(f"  ✅ {method_name}方法存在")
            else:
                print(f"  ❌ {method_name}方法不存在")
                return False
        
        # 测试get_all_violations方法
        try:
            violations = window.get_all_violations()
            print(f"  ✅ get_all_violations方法调用成功，返回{len(violations)}条违例")
        except Exception as e:
            print(f"  ❌ get_all_violations方法调用失败: {e}")
            return False
        
        print("✅ Web展示方法测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Web展示方法测试失败: {e}")
        return False


def test_status_bar_integration():
    """测试状态栏与Web展示功能的集成"""
    print("\n🧪 测试状态栏与Web展示功能的集成...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        
        # 创建QApplication实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 导入TimingViolationWindow
        from plugins.user.timing_violation.main_window import TimingViolationWindow
        
        # 创建窗口实例
        window = TimingViolationWindow()
        
        # 模拟Web展示状态更新
        try:
            # 测试各种状态消息
            test_messages = [
                ("网页展示已启动", 3000),
                ("网页服务已停止", 3000),
                ("网页服务: 服务运行在 http://localhost:8000", 0),
                ("数据更新: 正在生成网页数据...", 2000)
            ]
            
            for message, timeout in test_messages:
                window.status_bar.showMessage(message, timeout)
                print(f"  ✅ 状态消息设置成功: {message}")
            
        except Exception as e:
            print(f"  ❌ 状态消息设置失败: {e}")
            return False
        
        # 测试Web服务器状态回调
        try:
            if hasattr(window, 'on_web_server_status_changed'):
                window.on_web_server_status_changed(True, "服务运行在 http://localhost:8000")
                print("  ✅ Web服务器状态回调测试成功")
            else:
                print("  ❌ on_web_server_status_changed方法不存在")
                return False
        except Exception as e:
            print(f"  ❌ Web服务器状态回调测试失败: {e}")
            return False
        
        # 测试数据更新状态回调
        try:
            if hasattr(window, 'on_web_data_update_status'):
                window.on_web_data_update_status("正在生成网页数据...")
                print("  ✅ 数据更新状态回调测试成功")
            else:
                print("  ❌ on_web_data_update_status方法不存在")
                return False
        except Exception as e:
            print(f"  ❌ 数据更新状态回调测试失败: {e}")
            return False
        
        print("✅ 状态栏集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 状态栏集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 状态栏修复验证测试")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        ("状态栏创建", test_status_bar_creation),
        ("Web展示方法", test_web_display_methods),
        ("状态栏集成", test_status_bar_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试出现异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！status_bar属性错误已修复。")
        print("\n💡 现在可以安全地在GUI中点击'网页显示'按钮了！")
        return True
    else:
        print(f"\n❌ 有 {total - passed} 个测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)