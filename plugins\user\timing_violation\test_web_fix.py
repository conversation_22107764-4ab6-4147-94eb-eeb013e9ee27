#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Web显示修复
验证JavaScript错误修复是否有效
"""

import os
import sys
import json
import time
import webbrowser
import subprocess
from pathlib import Path

def test_web_fix():
    """测试Web显示修复"""
    print("🔧 测试Web显示修复")
    print("=" * 50)
    
    # 检查数据文件
    web_dir = Path("VIOLATION_CHECK/web_display")
    data_dir = web_dir / "data"
    
    print("📁 检查数据文件...")
    
    # 检查index.json
    index_file = data_dir / "index.json"
    if index_file.exists():
        with open(index_file, 'r', encoding='utf-8') as f:
            index_data = json.load(f)
        print(f"✅ index.json存在，corners: {len(index_data.get('corners', []))}, cases: {len(index_data.get('cases', []))}")
    else:
        print("❌ index.json不存在")
    
    # 检查violations.json
    violations_file = data_dir / "violations.json"
    if violations_file.exists():
        with open(violations_file, 'r', encoding='utf-8') as f:
            violations_data = json.load(f)
        
        if isinstance(violations_data, dict):
            violations_count = len(violations_data.get('violations', []))
            print(f"✅ violations.json存在（对象格式），violations: {violations_count}")
        elif isinstance(violations_data, list):
            print(f"✅ violations.json存在（数组格式），violations: {len(violations_data)}")
        else:
            print("⚠️ violations.json格式未知")
    else:
        print("❌ violations.json不存在")
    
    # 检查JavaScript文件
    js_file = web_dir / "js" / "app.js"
    if js_file.exists():
        with open(js_file, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # 检查修复是否存在
        if "Array.isArray(rawData)" in js_content:
            print("✅ JavaScript修复已应用")
        else:
            print("❌ JavaScript修复未应用")
    else:
        print("❌ app.js不存在")
    
    print("\n🚀 启动Web服务器测试...")
    
    # 启动Web服务器
    try:
        import http.server
        import socketserver
        import threading
        
        # 切换到web目录
        os.chdir(web_dir)
        
        # 查找可用端口
        port = 8001
        for test_port in range(8001, 8021):
            try:
                with socketserver.TCPServer(("", test_port), http.server.SimpleHTTPRequestHandler) as httpd:
                    port = test_port
                    break
            except OSError:
                continue
        
        print(f"🌐 启动Web服务器在端口 {port}")
        
        # 启动服务器
        httpd = socketserver.TCPServer(("", port), http.server.SimpleHTTPRequestHandler)
        server_thread = threading.Thread(target=httpd.serve_forever)
        server_thread.daemon = True
        server_thread.start()
        
        # 等待服务器启动
        time.sleep(2)
        
        # 打开浏览器
        url = f"http://localhost:{port}"
        print(f"🌍 打开浏览器: {url}")
        webbrowser.open(url)
        
        print("\n" + "=" * 50)
        print("🎉 Web服务器已启动！")
        print(f"📱 访问地址: {url}")
        print("🔍 请检查浏览器控制台是否还有错误")
        print("⏹️ 按 Ctrl+C 停止服务器")
        print("=" * 50)
        
        # 等待用户中断
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 停止Web服务器...")
            httpd.shutdown()
            httpd.server_close()
            
    except Exception as e:
        print(f"❌ 启动Web服务器失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    try:
        test_web_fix()
    except KeyboardInterrupt:
        print("\n👋 测试中断")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        sys.exit(1)